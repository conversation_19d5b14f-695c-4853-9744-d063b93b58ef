import React, {useState, useEffect} from 'react';
import {useForm} from 'react-hook-form';
import {useTranslation} from 'react-i18next';
import {
  Pressable,
  KeyboardAvoidingView,
  View,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {validatePhoneNumber} from '../../../utils/validate';
import {FAddressPickerForm} from '../../Default/form/component-form';
import {TextFieldForm} from '../../news/form/component-form';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import {TypoSkin} from '../../../assets/skin/typography';
import {AppButton, ListTile} from 'wini-mobile-components';
import IOSSwitch from '../../../components/IOSSwitch';
import WScreenFooter from '../../../Screen/Layout/footer';
import {navigateBack, RootScreen} from '../../../router/router';
import {DataController} from '../../../base/baseController';
import {useDispatch} from 'react-redux';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {randomGID} from '../../../utils/Utils';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {useRoute} from '@react-navigation/native';

export default function EditAddress() {
  const {t} = useTranslation();
  const route = useRoute<any>();
  const item = route?.params?.item;
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {Id: randomGID(), DateCreated: new Date().getTime()},
  });

  const [isDefault, setIsDefault] = useState(false);

  const dispatch = useDispatch<any>();
  const customer = useSelectorCustomerState().data;
  const addresses = useSelectorCustomerState().myAddress;

  useEffect(() => {
    if (item) {
      Object.keys(item).forEach(key => {
        methods.setValue(key, item[key]);
      });
      setIsDefault(item.IsDefault);
    }
  }, [item]);

  useEffect(() => {
    if (addresses && addresses.length == 0) {
      setIsDefault(true);
    }
  });

  const onSubmit = () => {
    const data = methods.getValues();
    const obj = {
      ...data,
      CustomerId: customer.Id,
      IsDefault: isDefault,
    };
    if (item) {
      data.Id = item.Id;
      data.DateCreated = item.DateCreated;
    }
    dispatch(CustomerActions.editAddress(obj, item ? false : true));
    navigateBack();
  };

  return (
    <Pressable
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <InforHeader title="Chi tiết địa chỉ" />
      <TouchableWithoutFeedback
        style={{flex: 1}}
        onPress={() => Keyboard.dismiss()}>
        <KeyboardAvoidingView>
          <View
            style={{
              paddingHorizontal: 16,
              gap: 16,
              paddingVertical: 8,
              paddingBottom: 100,
            }}>
            <TextFieldForm
              required
              style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
              placeholder={t('profile.name')}
              label={t('profile.name')}
              control={methods.control}
              errors={methods.formState.errors}
              register={methods.register}
              name="Name"
              textFieldStyle={{padding: 16}}
            />
            <TextFieldForm
              style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
              placeholder={t('profile.email')}
              label={t('profile.email')}
              control={methods.control}
              errors={methods.formState.errors}
              register={methods.register}
              name="Email"
              textFieldStyle={{padding: 16}}
            />
            <TextFieldForm
              required
              style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
              placeholder={t('profile.phone')}
              label={t('profile.phone')}
              control={methods.control}
              register={methods.register}
              errors={methods.formState.errors}
              name="Mobile"
              textFieldStyle={{padding: 16}}
              type="number-pad"
              onBlur={(ev: string) => {
                // Check if the number doesn't already start with 0 or +84
                if (/^(\+84|0)/.test(ev)) {
                  const val = validatePhoneNumber(ev);
                  if (!val) {
                    methods.setError('Mobile', {
                      message: t('profile.invalidPhone'),
                    });
                    return;
                  }
                }
                if (ev?.length !== 0) methods.clearErrors('Mobile');
                else
                  methods.setError('Mobile', {
                    message: t('profile.phoneRequired'),
                  });
              }}
            />
            <FAddressPickerForm
              required
              control={methods.control}
              errors={methods.formState.errors}
              name="Address"
              label={t('profile.address')}
              placeholder={t('profile.address')}
              textFieldStyle={{
                paddingLeft: 16,
                gap: 12,
              }}
              onChange={value => {
                methods.setValue('Long', value.geometry.location.lng);
                methods.setValue('Lat', value.geometry.location.lat);
                methods.setValue('Address', value.formatted_address);
                return value.formatted_address;
              }}
            />
            <ListTile
              title="Đặt làm địa chỉ mặc định"
              style={{padding: 0, marginTop: 16}}
              titleStyle={[
                TypoSkin.heading8,
                {
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                },
              ]}
              trailing={
                <IOSSwitch
                  disabled={addresses && addresses.length == 0}
                  value={isDefault}
                  onColor={ColorThemes.light.primary_main_color}
                  onValueChange={setIsDefault}
                />
              }
            />
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
      <WScreenFooter>
        <AppButton
          containerStyle={{
            borderRadius: 8,
            marginHorizontal: 16,
            flex: 1,
            backgroundColor: ColorThemes.light.primary_main_color,
            justifyContent: 'center',
          }}
          borderColor="transparent"
          title={'Lưu'}
          onPress={() => {
            methods.handleSubmit(onSubmit)();
          }}
        />
      </WScreenFooter>
    </Pressable>
  );
}
