import React, {useState, useEffect, useCallback, useMemo, useRef} from 'react';
import {
  StyleSheet,
  FlatList,
  RefreshControl,
  Dimensions,
  ScrollView,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation, useRoute} from '@react-navigation/native';
import {ColorThemes} from '../../../assets/skin/colors';
import {ProductDA, ProductItem} from '../productDA';
import categoryDA from '../../category/categoryDA';
import ProductCard from '../card/ProductCard';
import {RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import ProductListSkeleton from './components/ProductListSkeleton';
import CategoryHeader from '../../../Screen/Layout/headers/CategoryHeader';
import ScrollableTabs from '../../news/scrollable/ScrollableTabs';
import {
  faBookOpen,
  faFire,
  faHeart,
  faTruckFast,
} from '@fortawesome/free-solid-svg-icons';
import CategoryTabs from './components/CategoryTabs';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {Category} from '../../../redux/models/category';

const {width} = Dimensions.get('window');

const TABS_DATA = [
  {
    id: 'hot',
    label: 'HOT',
    icon: faFire,
  },
  {
    id: 'freeship',
    label: 'Freeship',
    icon: faTruckFast,
    color: '#3FB993',
  },
  {
    id: 'new',
    label: 'Mới',
    icon: faBookOpen,
  },
  {
    id: 'favorite',
    label: 'Nhãn hàng ưa chuộng',
    icon: faHeart,
  },
];

const MemoizedProductCard = React.memo(ProductCard);

export default function ProductListByCategory() {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const {categoryId} = route.params || {};
  const categoryScrollViewRef = useRef<ScrollView>(null);

  // store
  const {data} = useSelector((state: RootState) => state.category);

  // State management
  const [products, setProducts] = useState<ProductItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>(
    categoryId || '',
  );
  const [selectedFilter, setSelectedFilter] = useState<string>('hot');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Data access objects
  const productDA = useMemo(() => new ProductDA(), []);
  const categoryDataAccess = useMemo(() => new categoryDA(), []);

  const scrollToCategory = useCallback((catId: string, cats: Category[]) => {
    if (categoryScrollViewRef.current) {
      const categoryIndex = cats.findIndex(category => category.Id === catId);
      if (categoryIndex !== -1) {
        // A simple estimation for scroll position. For more accuracy, consider using onLayout.
        const scrollPosition = categoryIndex * 100;
        categoryScrollViewRef.current.scrollTo({
          x: scrollPosition,
          animated: true,
        });
      }
    }
  }, []);

  // Fetch products based on selected category and filter
  const fetchProducts = useCallback(
    async (reset = false) => {
      if (!selectedCategory) return;

      const currentPage = reset ? 1 : page;
      if (currentPage === 1) setIsLoading(true);

      try {
        const isHot = selectedFilter === 'hot';
        const result = await productDA.getAllListbyCategory(
          currentPage,
          20,
          selectedCategory,
          isHot,
        );

        if (result?.data) {
          setProducts(prev =>
            reset ? result.data : [...prev, ...result.data],
          );
          setHasMore(result.data.length === 20);
          setPage(currentPage + 1);
        } else {
          setHasMore(false);
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    },
    [productDA, selectedCategory, selectedFilter, page],
  );

  useEffect(() => {
    fetchProducts(true);
  }, [selectedCategory, selectedFilter]); // Simplified dependency

  // Handle refresh
  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    fetchProducts(true);
  }, [fetchProducts]);

  // Handle load more
  const onLoadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      fetchProducts(false);
    }
  }, [isLoading, hasMore, fetchProducts]);

  // Handle category selection
  const handleCategorySelect = useCallback(
    (newCategoryId: string) => {
      setSelectedCategory(newCategoryId);
      scrollToCategory(newCategoryId, data);
    },
    [data, scrollToCategory],
  );

  // Handle product press
  const handleProductPress = useCallback(
    (product: ProductItem) => {
      navigation.push(RootScreen.ProductDetail, {id: product.Id});
    },
    [navigation],
  );

  // Render product item
  const renderProductItem = useCallback(
    ({item}: {item: ProductItem}) => (
      <MemoizedProductCard
        item={item}
        onPress={handleProductPress}
        width={(width - 48) / 2}
        height={((width - 48) / 2) * 1.8}
      />
    ),
    [handleProductPress],
  );

  const renderListEmpty = useCallback(() => {
    if (isLoading) {
      return <ProductListSkeleton />;
    }
    return <EmptyPage />;
  }, [isLoading]);

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <CategoryHeader />
      <ScrollableTabs onChangeTab={() => {}} data={TABS_DATA} />
      <CategoryTabs
        categories={data}
        selectedCategory={selectedCategory}
        onSelectCategory={handleCategorySelect}
        scrollViewRef={categoryScrollViewRef}
      />
      <FlatList
        data={products}
        renderItem={renderProductItem}
        keyExtractor={(item, index) => item.Id || index.toString()}
        numColumns={2}
        contentContainerStyle={styles.productList}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        ListEmptyComponent={renderListEmpty}
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  productList: {
    padding: 16,
    gap: 16,
  },
});
