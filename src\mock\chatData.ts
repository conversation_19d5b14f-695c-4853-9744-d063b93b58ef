import { Chat<PERSON><PERSON>, ChatMessage, ChatUser } from '../modules/chat/types/ChatTypes';

// Mock users
export const mockUsers: ChatUser[] = [
  {
    _id: '1',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
  },
  {
    _id: '2',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
  },
  {
    _id: '3',
    name: '<PERSON><PERSON>',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
  },
  {
    _id: '4',
    name: '<PERSON><PERSON><PERSON>',
    avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
  },
  {
    _id: '5',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
  },
];

// Mock chat rooms
export const mockChatRooms: ChatRoom[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    type: 'private',
    participants: [mockUsers[0], mockUsers[1]],
    lastMessage: {
      _id: '1',
      text: 'Chào bạn! Bạn có khỏe không?',
      createdAt: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
      user: mockUsers[0],
    },
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 5),
    unreadCount: 2,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 5),
  },
  {
    id: '2',
    name: 'Nhóm dự án ABC',
    type: 'group',
    participants: [mockUsers[0], mockUsers[1], mockUsers[2], mockUsers[3]],
    lastMessage: {
      _id: '2',
      text: 'Meeting lúc 2h chiều nhé mọi người',
      createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      user: mockUsers[2],
    },
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 30),
    unreadCount: 5,
    avatar: 'https://via.placeholder.com/50/4169E1/FFFFFF?text=ABC',
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 30),
  },
  {
    id: '3',
    name: 'Trần Thị B',
    type: 'private',
    participants: [mockUsers[1], mockUsers[0]],
    lastMessage: {
      _id: '3',
      text: 'Cảm ơn bạn nhiều! 😊',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      user: mockUsers[1],
    },
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 2),
    unreadCount: 0,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
  },
  {
    id: '4',
    name: 'Team Marketing',
    type: 'group',
    participants: [mockUsers[0], mockUsers[3], mockUsers[4]],
    lastMessage: {
      _id: '4',
      text: 'Chiến dịch mới đã được approve',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      user: mockUsers[4],
    },
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 6),
    unreadCount: 1,
    avatar: 'https://via.placeholder.com/50/FF6B6B/FFFFFF?text=MKT',
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 1 week ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 6),
  },
];

// Mock messages for each room
export const mockMessages: { [roomId: string]: ChatMessage[] } = {
  '1': [
    {
      _id: '1',
      text: 'Chào bạn! Bạn có khỏe không?',
      createdAt: new Date(Date.now() - 1000 * 60 * 5),
      user: mockUsers[0],
      sent: true,
      received: true,
    },
    {
      _id: '2',
      text: 'Mình khỏe, cảm ơn bạn! Còn bạn thì sao?',
      createdAt: new Date(Date.now() - 1000 * 60 * 3),
      user: mockUsers[1],
      sent: true,
      received: true,
    },
    {
      _id: '3',
      text: 'Mình cũng ổn. Hôm nay có gì mới không?',
      createdAt: new Date(Date.now() - 1000 * 60 * 1),
      user: mockUsers[0],
      sent: true,
      received: true,
    },
  ],
  '2': [
    {
      _id: '4',
      text: 'Chào mọi người!',
      createdAt: new Date(Date.now() - 1000 * 60 * 60),
      user: mockUsers[0],
      sent: true,
      received: true,
    },
    {
      _id: '5',
      text: 'Hi team! 👋',
      createdAt: new Date(Date.now() - 1000 * 60 * 50),
      user: mockUsers[1],
      sent: true,
      received: true,
    },
    {
      _id: '6',
      text: 'Meeting lúc 2h chiều nhé mọi người',
      createdAt: new Date(Date.now() - 1000 * 60 * 30),
      user: mockUsers[2],
      sent: true,
      received: true,
    },
    {
      _id: '7',
      text: 'OK, mình sẽ có mặt đúng giờ',
      createdAt: new Date(Date.now() - 1000 * 60 * 25),
      user: mockUsers[3],
      sent: true,
      received: true,
    },
  ],
  '3': [
    {
      _id: '8',
      text: 'Bạn có thể giúp mình review document này không?',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 3),
      user: mockUsers[0],
      sent: true,
      received: true,
    },
    {
      _id: '9',
      text: 'Được chứ, bạn gửi link cho mình nhé',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2.5),
      user: mockUsers[1],
      sent: true,
      received: true,
    },
    {
      _id: '10',
      text: 'Cảm ơn bạn nhiều! 😊',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
      user: mockUsers[1],
      sent: true,
      received: true,
    },
  ],
  '4': [
    {
      _id: '11',
      text: 'Team ơi, có update gì về campaign mới không?',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 8),
      user: mockUsers[0],
      sent: true,
      received: true,
    },
    {
      _id: '12',
      text: 'Mình đang chờ feedback từ client',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 7),
      user: mockUsers[3],
      sent: true,
      received: true,
    },
    {
      _id: '13',
      text: 'Chiến dịch mới đã được approve',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 6),
      user: mockUsers[4],
      sent: true,
      received: true,
    },
  ],
};

// Function to get mock data (simulate API calls)
export const getMockChatRooms = (): Promise<{ data: ChatRoom[]; total: number }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: mockChatRooms,
        total: mockChatRooms.length,
      });
    }, 500); // Simulate network delay
  });
};

export const getMockMessages = (roomId: string): Promise<{ data: ChatMessage[]; total: number }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const messages = mockMessages[roomId] || [];
      resolve({
        data: messages.reverse(), // GiftedChat expects newest first
        total: messages.length,
      });
    }, 300);
  });
};

export const getMockUsers = (query: string): Promise<ChatUser[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const filteredUsers = mockUsers.filter(user =>
        user.name.toLowerCase().includes(query.toLowerCase())
      );
      resolve(filteredUsers);
    }, 300);
  });
};
