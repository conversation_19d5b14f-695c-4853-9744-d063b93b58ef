import io, { Socket } from 'socket.io-client';
import { ChatMessage, ChatRoom } from '../types/ChatTypes';
import ConfigAPI from '../../../Config/ConfigAPI';

class SocketService {
  private socket: Socket | null = null;
  private isConnected: boolean = false;

  connect(userId: string, token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = io(ConfigAPI.url.replace('/api/', ''), {
          auth: {
            token,
            userId,
          },
          transports: ['websocket'],
        });

        this.socket.on('connect', () => {
          console.log('Socket connected');
          this.isConnected = true;
          resolve();
        });

        this.socket.on('disconnect', () => {
          console.log('Socket disconnected');
          this.isConnected = false;
        });

        this.socket.on('connect_error', (error) => {
          console.error('Socket connection error:', error);
          this.isConnected = false;
          reject(error);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  joinRoom(roomId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_room', { roomId });
    }
  }

  leaveRoom(roomId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave_room', { roomId });
    }
  }

  sendMessage(roomId: string, message: Partial<ChatMessage>) {
    if (this.socket && this.isConnected) {
      this.socket.emit('send_message', {
        roomId,
        message,
      });
    }
  }

  onNewMessage(callback: (message: ChatMessage) => void) {
    if (this.socket) {
      this.socket.on('new_message', callback);
    }
  }

  onMessageUpdate(callback: (message: ChatMessage) => void) {
    if (this.socket) {
      this.socket.on('message_update', callback);
    }
  }

  onRoomUpdate(callback: (room: ChatRoom) => void) {
    if (this.socket) {
      this.socket.on('room_update', callback);
    }
  }

  onUserTyping(callback: (data: { roomId: string; userId: string; isTyping: boolean }) => void) {
    if (this.socket) {
      this.socket.on('user_typing', callback);
    }
  }

  sendTyping(roomId: string, isTyping: boolean) {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing', { roomId, isTyping });
    }
  }

  removeAllListeners() {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }
}

export default new SocketService();
