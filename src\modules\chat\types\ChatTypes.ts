export interface ChatMessage {
  _id: string;
  text: string;
  createdAt: Date;
  user: ChatUser;
  image?: string;
  video?: string;
  audio?: string;
  system?: boolean;
  sent?: boolean;
  received?: boolean;
  pending?: boolean;
  quickReplies?: QuickReplies;
}

export interface ChatUser {
  _id: string | number;
  name: string;
  avatar?: string;
}

export interface QuickReplies {
  type: 'radio' | 'checkbox';
  values: QuickReply[];
  keepIt?: boolean;
}

export interface QuickReply {
  title: string;
  value: string;
  messageId?: any;
}

export interface ChatRoom {
  id: string;
  name: string;
  type: 'private' | 'group';
  participants: ChatUser[];
  lastMessage?: ChatMessage;
  lastMessageTime?: Date;
  unreadCount?: number;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatState {
  rooms: ChatRoom[];
  currentRoom: ChatRoom | null;
  messages: { [roomId: string]: ChatMessage[] };
  loading: boolean;
  error: string | null;
  isConnected: boolean;
}

export interface CreateGroupRequest {
  name: string;
  participants: string[];
  avatar?: string;
}

export interface SendMessageRequest {
  roomId: string;
  text?: string;
  image?: string;
  video?: string;
  audio?: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'file';
}
