


import { StyleSheet, TouchableOpacity, View } from "react-native";
import { AppSvg, Winicon } from "wini-mobile-components";
import { ColorThemes } from "../../../../assets/skin/colors";
import { Text } from "react-native-paper";
import { TypoSkin } from "../../../../assets/skin/typography";
import { useNavigation } from "@react-navigation/native";
import { Title } from "../../../../Config/Contanst";
import { RootScreen } from "../../../../router/router";

interface MenuCardsProps {
    svgIcon: string
    color: string
    title: string
    order: string
}



const MenuCards = (props: MenuCardsProps) => {
    let { svgIcon, color, title, order } = props;
    const navigation = useNavigation<any>();
    const handleNavigateOrders = (
        order: string,
    ) => {
        navigation.navigate(order);
    };
    return (
        <TouchableOpacity
            style={styles.actionCard}
            onPress={() =>
                handleNavigateOrders(order)
            }>
            <AppSvg SvgSrc={svgIcon} size={20} />
            <Text style={styles.actionText}>{title}</Text>
        </TouchableOpacity>
    )
}

const styles = StyleSheet.create({
    actionCard: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
        borderRadius: 20,
        margin: 10,
        padding: 15,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
        height: 70,
    },
    actionText: {
        ...TypoSkin.title4,
        color: ColorThemes.light.neutral_text_title_color,
        marginLeft: 10,
    },

});

export default MenuCards;


