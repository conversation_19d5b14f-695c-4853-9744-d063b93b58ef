import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Text,
} from 'react-native';
import Svg, {Path} from 'react-native-svg';
import {BottomTabBarProps} from '@react-navigation/bottom-tabs';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../assets/skin/colors';
import {dialogCheckAcc} from '../Screen/Layout/mainLayout';
import {useSelectorCustomerState} from '../redux/hook/customerHook';

const {width} = Dimensions.get('window');
const height = 60;

const CustomTabBar = ({props, ref}: any) => {
  const {state, descriptors, navigation} = props;
  const centerWidth = 75; // Tăng độ rộng của phần giữa
  const tabWidth = width;
  const center = tabWidth / 2 - centerWidth / 2;
  const curveHeight = 40; // Tăng độ cao của đường cong

  // <PERSON><PERSON><PERSON> định tab đang được chọn
  const focusedOptions = descriptors[state.routes[state.index].key].options;

  const customer = useSelectorCustomerState().data;

  // Nếu tab đang được chọn ẩn tab bar, thì không hiển thị tab bar
  if (
    focusedOptions.tabBarVisible === false ||
    focusedOptions.tabBarStyle?.display === 'none'
  ) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* SVG curved background */}
      <Svg
        width={tabWidth}
        height={height}
        viewBox={`0 0 ${tabWidth} ${height}`}>
        <Path
          fill="#fff"
          d={`M0 20
              Q0 0 20 0
              H${center - 20}
              Q${center - 10} 0, ${center} ${curveHeight / 3}
              C${center + centerWidth / 4} ${curveHeight}, ${
            center + (3 * centerWidth) / 4
          } ${curveHeight}, ${center + centerWidth} ${curveHeight / 3}
              Q${center + centerWidth + 10} 0, ${center + centerWidth + 20} 0
              H${tabWidth - 20}
              Q${tabWidth} 0 ${tabWidth} 20
              V${height} H0 Z`}
          stroke={ColorThemes.light.primary_border_color}
          strokeWidth="0.5"
        />
      </Svg>

      {/* Tab buttons */}
      <View style={styles.tabButtonContainer}>
        {state.routes.map((route: any, index: number) => {
          const isFocused = state.index === index;
          const isCenter = index === Math.floor(state.routes.length / 2);

          // Nếu là tab ở giữa, không hiển thị nút
          if (isCenter) {
            return <View key={index} style={{width: centerWidth}} />;
          }

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          return (
            <TouchableOpacity
              key={index}
              style={styles.tabButton}
              onPress={onPress}
              activeOpacity={0.7}>
              <Winicon
                key={route.name}
                src={getIconName(route.name, isFocused)}
                size={20}
                color={isFocused ? '#4169E1' : '#AAAAAA'}
              />
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Center QR button */}
      <TouchableOpacity
        style={styles.centerButton}
        onPress={() => {
          // Tìm route QR Code (index 2)
          const qrRoute = state.routes.find(
            (route: any) => route.name === 'QRCode',
          );

          if (!customer) {
            dialogCheckAcc(ref);
            return;
          }

          if (qrRoute) {
            // Điều hướng đến QR Code route
            navigation.navigate(qrRoute.name);
          }
        }}>
        <View style={styles.qrButton}>
          <Winicon src="fill/shopping/barcode-qr" size={24} color="#1C33FF" />
        </View>
      </TouchableOpacity>
    </View>
  );
};

// Hàm helper để lấy tên icon dựa trên tên route và trạng thái focus
const getIconName = (routeName: string, isFocused: boolean): string => {
  const prefix = isFocused ? 'fill' : 'outline';

  switch (routeName) {
    case 'home':
      return `${prefix}/user interface/home`;
    case 'news':
      return `${prefix}/user interface/news`;
    case 'category':
      return `${prefix}/layout/grid-list`;
    case 'QRCode':
      return `fill/user interface/qr-code`;
    case 'community':
      return `${prefix}/user interface/network-communication`;
    case 'call':
      return `${prefix}/user interface/f-chat`;
    case 'profile':
      return `${prefix}/users/profile`;
    default:
      return `${prefix}/user interface/apps`;
  }
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    width,
  },
  tabButtonContainer: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 0,
    width: '100%',
    height,
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  tabButton: {
    flex: 1,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerButton: {
    position: 'absolute',
    top: -32, // Điều chỉnh vị trí cao hơn để phù hợp với đường cong mới
    left: width / 2 - 30,
    width: 60,
    height: 60,
    borderRadius: 150,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    elevation: 5,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    borderWidth: 0.5,
    borderColor: ColorThemes.light.primary_border_color,
  },
  qrButton: {
    width: 52,
    height: 52,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CustomTabBar;
