import React, {useEffect, useCallback, memo} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
  Text,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {AppSvg, FDialog} from 'wini-mobile-components';
import {navigate, RootScreen} from '../../../router/router';
import CartIcon from '../../../components/CartIcon';
import iconSvg from '../../../svg/icon';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import HeaderBackground from '../../../components/shop/HeaderShop';
import {dialogCheckAcc} from '../mainLayout';
import {useCategoryHook} from '../../../redux/hook/categoryHook';
import {ColorThemes} from '../../../assets/skin/colors';
import SearchBar from '../../../components/shop/Search';

const HeaderIconButton = memo(
  ({onPress, children}: {onPress?: () => void; children: React.ReactNode}) => (
    <TouchableOpacity style={styles.iconButton} onPress={onPress}>
      <View style={styles.iconCircle}>{children}</View>
    </TouchableOpacity>
  ),
);

const RightHeaderActions = memo(
  ({
    onNotificationPress,
    onCartPress,
  }: {
    onNotificationPress: () => void;
    onCartPress: () => void;
  }) => (
    <View style={styles.rightIcons}>
      <Image
        source={require('../../../assets/images/logo.png')}
        style={{width: 30, height: 30, borderRadius: 20}}
      />
      <HeaderIconButton onPress={onNotificationPress}>
        <AppSvg SvgSrc={iconSvg.notification} size={16} />
      </HeaderIconButton>
      <HeaderIconButton onPress={onCartPress}>
        <CartIcon isHome color="#0033CC" size={18} showBadge={true} />
      </HeaderIconButton>
    </View>
  ),
);

const CategoryHeader = () => {
  const categoryHook = useCategoryHook();
  const customer = useSelectorCustomerState().data;
  const dialogRef = React.useRef<any>(null);

  useEffect(() => {
    if (Platform.OS === 'android') {
      StatusBar.setTranslucent(true);
      StatusBar.setBackgroundColor('transparent');
    }
    StatusBar.setBarStyle('dark-content');

    return () => {
      if (Platform.OS === 'android') {
        StatusBar.setTranslucent(false);
        StatusBar.setBackgroundColor('#FFA500'); // Example color
      }
    };
  }, []);

  const handleMenuPress = () => {
    categoryHook.setData('showDrawer', true);
  };

  const handleProtectedAction = useCallback(
    (action: () => void) => {
      if (!customer?.Id) {
        dialogCheckAcc(dialogRef);
        return;
      }
      action();
    },
    [customer],
  );

  const handleNotificationPress = () =>
    handleProtectedAction(() => {
      navigate(RootScreen.ProductListByCategory);
    });

  const handleCartPress = () =>
    handleProtectedAction(() => {
      navigate(RootScreen.CartPage);
    });

  return (
    <View style={styles.header}>
      <FDialog ref={dialogRef} />
      <View style={styles.headerBackground}>
        <HeaderBackground />
      </View>
      <View style={styles.headerContent}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <TouchableOpacity onPress={handleMenuPress}>
            <AppSvg SvgSrc={iconSvg.menuUnfold} size={25} />
          </TouchableOpacity>
          <Text
            style={{
              fontSize: 16,
              fontWeight: 'bold',
              marginLeft: 10,
              color: ColorThemes.light.infor_text_color,
            }}>
            Thời trang
          </Text>
        </View>
        <RightHeaderActions
          onNotificationPress={handleNotificationPress}
          onCartPress={handleCartPress}
        />
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <View style={{width: '90%'}}>
          <SearchBar setDataSearch={() => {}} />
        </View>
        <AppSvg
          SvgSrc={iconSvg.filter}
          color="#000"
          size={25}
          style={{marginRight: 12}}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    width: '100%',
    position: 'relative',
    paddingTop: 16,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  headerContent: {
    marginTop: 40,
    width: '100%',
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 2,
    paddingHorizontal: 12,
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 8,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
});

export default CategoryHeader;
