import React, {use, useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Image,
} from 'react-native';
import {Checkbox, ListTile, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faAngleRight} from '@fortawesome/free-solid-svg-icons';
import {FlatList} from 'react-native';
import EmptyPage from '../../../../Screen/emptyPage';

const ItemPromorionCard = (
  data: any,
  selectedDataOne: string,
  setSelectedDataOne: (data: string) => void,
) => {
  const [SelectedDataMenuOne, setSelectedDataMenuOne] = useState<string>('');
  if (data?.data?.length < 1) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <EmptyPage />
      </View>
    );
  }

  const handleSelectedDataMenuOne = (data: string) => {
    setSelectedDataMenuOne(data);
  };

  return (
    <View style={{height: '100%', width: '100%'}}>
      {data?.data?.map((item: any) => (
        <View style={styles.itemContainer}>
          <View style={styles.itemText}>
            {/* <Image
            source={{
              uri: 'https://www.bing.com/images/search?q=%E1%BA%A3nh%20hoa%20tulip&FORM=IQFRBA&id=C28E1774AF851135762839EAC46C08C03FC6CB01',
            }}
            style={{width: 32, height: 32, borderRadius: 100}}
          /> */}
            <Checkbox value={true} onChange={() => {}} size={24} />
            <Text style={{marginLeft: 10, fontSize: 20, fontFamily: 'roboto'}}>
              {item?.Name}
            </Text>
          </View>
          <TouchableOpacity style={{marginRight: 10}}>
            <FontAwesomeIcon
              icon={faAngleRight}
              color={ColorThemes.light.black}
              size={16}
            />
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 65,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    marginLeft: 20,
  },
  itemText: {
    fontSize: 20,
    color: 'black',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',

    marginLeft: 20,
    marginRight: 10,
  },
});

export default ItemPromorionCard;
