import React from 'react';
import {useForm} from 'react-hook-form';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import {TextFieldForm} from '../../../modules/news/form/component-form';
import {ColorThemes} from '../../../assets/skin/colors';

const {width} = Dimensions.get('window');

const ModalPromotion = ({
  isShow,
  closeModal,
  svgSrc,
  title,
}: {
  isShow: boolean;
  closeModal: () => void;
  svgSrc: string;
  title: string;
}) => {
  const handleConfirm = () => {
    closeModal();
  };
  const methods = useForm({shouldFocusError: false});
  let textFieldStyle = {
    height: 48,
    paddingLeft: 8,
    paddingRight: 8,
    borderWidth: 0,
  };
  return (
    <View style={styles.container}>
      {/* Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={isShow}
        onRequestClose={closeModal}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {/* Icon cảnh báo */}
            <AppSvg SvgSrc={svgSrc} size={60} />

            {/* Nội dung thông báo */}
            <Text style={styles.modalText}>{title}</Text>
            <View style={{width: '100%'}}>
              <Text style={{fontSize: 16, fontWeight: '600'}}>Nhập số %</Text>
              <View>
                <TextFieldForm
                  control={methods.control}
                  name="Content"
                  placeholder="Nhập số % đánh giá"
                  returnKeyType="done"
                  textFieldStyle={textFieldStyle}
                  errors={methods.formState.errors}
                  register={methods.register}
                  required
                  style={styles.input}
                />
              </View>
            </View>

            {/* Container cho các nút */}
            <View style={styles.buttonContainer}>
              {/* Nút Hủy */}
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={closeModal}
                activeOpacity={0.8}>
                <Text style={styles.cancelButtonText}>Đóng</Text>
              </TouchableOpacity>

              {/* Nút Đồng ý */}
              <TouchableOpacity
                style={[styles.button, styles.agreeButton]}
                onPress={handleConfirm}
                activeOpacity={0.8}>
                <Text style={styles.agreeButtonText}>Đồng ý</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  showButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  showButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    width: width * 0.8,
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  warningIcon: {
    width: 50,
    height: 50,
    backgroundColor: '#FF8C00',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  warningIconText: {
    color: 'white',
    fontSize: 28,
    fontWeight: 'bold',
  },
  modalText: {
    fontSize: 20,
    marginTop: 12,
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    color: 'black',
  },
  cancelButton: {
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  agreeButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  agreeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  input: {
    borderRadius: 10,
    fontSize: 16,
    color: '#000',
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: ColorThemes.light.neutral_main_border_color,
    marginBottom: 16,
    marginTop: 4,
  },
});

export default ModalPromotion;
