import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Platform,
} from 'react-native';
import { GiftedChat, IMessage, InputToolbar, Send, Bubble } from 'react-native-gifted-chat';
import { useDispatch } from 'react-redux';
import { useRoute, useNavigation } from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
// import DocumentPicker from 'react-native-document-picker'; // Removed due to compatibility issues
import { ColorThemes } from '../../../assets/skin/colors';
import { useChatMessages, useSelectorChatState } from '../../../redux/hook/chatHook';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import { 
  fetchMessages, 
  addMessage, 
  setCurrentRoom,
  resetUnreadCount 
} from '../../../redux/reducers/ChatReducer';
import { ChatRoom, ChatMessage } from '../types/ChatTypes';
import SocketService from '../services/SocketService';
import ChatAPI from '../services/ChatAPI';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import EmojiPicker from '../components/EmojiPicker';
import { PermissionHelper } from '../utils/PermissionHelper';

interface RouteParams {
  room: ChatRoom;
}

const ChatRoomScreen: React.FC = () => {
  const dispatch = useDispatch<any>();
  const navigation = useNavigation();
  const route = useRoute<any>();
  const { room } = route.params as RouteParams;
  
  const customer = useSelectorCustomerState().data;
  const messages = useChatMessages(room.id);
  const chatState = useSelectorChatState();
  
  const [isTyping, setIsTyping] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [inputText, setInputText] = useState('');

  useEffect(() => {
    if (room) {
      dispatch(setCurrentRoom(room));
      dispatch(fetchMessages(room.id, 1));
      dispatch(resetUnreadCount(room.id));
      
      // Join room for socket
      SocketService.joinRoom(room.id);
      
      // Setup socket listeners
      SocketService.onNewMessage((message) => {
        dispatch(addMessage({ roomId: room.id, message }));
      });
    }

    return () => {
      SocketService.leaveRoom(room.id);
      dispatch(setCurrentRoom(null));
    };
  }, [room.id, dispatch]);

  // Convert messages to GiftedChat format
  const giftedChatMessages: IMessage[] = messages.map((msg: ChatMessage) => ({
    _id: msg._id,
    text: msg.text,
    createdAt: new Date(msg.createdAt),
    user: {
      _id: msg.user._id,
      name: msg.user.name,
      avatar: msg.user.avatar,
    },
    image: msg.image,
    video: msg.video,
    audio: msg.audio,
    sent: msg.sent,
    received: msg.received,
    pending: msg.pending,
  }));

  const onSend = useCallback(async (newMessages: IMessage[] = []) => {
    const message = newMessages[0];
    
    try {
      // Add message to local state immediately
      const chatMessage: ChatMessage = {
        _id: message._id.toString(),
        text: message.text || '',
        createdAt: message.createdAt as Date,
        user: {
          _id: customer?.id || '',
          name: customer?.name || '',
          avatar: customer?.avatar,
        },
        pending: true,
      };
      
      dispatch(addMessage({ roomId: room.id, message: chatMessage }));
      
      // Send via socket
      SocketService.sendMessage(room.id, chatMessage);
      
      // Also send via API for persistence
      await ChatAPI.sendMessage({
        roomId: room.id,
        text: message.text || '',
        type: 'text',
      });
      
    } catch (error) {
      console.error('Error sending message:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể gửi tin nhắn',
      });
    }
  }, [room.id, customer, dispatch]);

  const onLoadEarlier = useCallback(async () => {
    if (!hasMoreMessages) return;
    
    try {
      const nextPage = page + 1;
      const response = await ChatAPI.getMessages(room.id, nextPage);
      
      if (response.data.length === 0) {
        setHasMoreMessages(false);
      } else {
        setPage(nextPage);
        // Messages will be added via fetchMessages action
        dispatch(fetchMessages(room.id, nextPage));
      }
    } catch (error) {
      console.error('Error loading earlier messages:', error);
    }
  }, [room.id, page, hasMoreMessages, dispatch]);

  const handleImagePicker = async () => {
    // Kiểm tra quyền trước khi hiển thị options
    const permissions = await PermissionHelper.checkAllChatPermissions();

    if (!permissions.camera && !permissions.photoLibrary) {
      PermissionHelper.showPermissionDeniedAlert('camera và thư viện ảnh');
      return;
    }

    const options = [
      { text: 'Hủy', style: 'cancel' as const },
    ];

    if (permissions.photoLibrary) {
      options.push({ text: 'Thư viện', onPress: () => pickImageFromLibrary() });
    }

    if (permissions.camera) {
      options.push({ text: 'Camera', onPress: () => pickImageFromCamera() });
    }

    Alert.alert('Chọn ảnh', 'Bạn muốn chọn ảnh từ đâu?', options);
  };

  const pickImageFromLibrary = () => {
    ImagePicker.openPicker({
      width: 800,
      height: 600,
      cropping: false,
      mediaType: 'photo',
    }).then(async (image) => {
      await sendImageMessage(image);
    }).catch((error) => {
      if (error.code !== 'E_PICKER_CANCELLED') {
        console.error('Image picker error:', error);
      }
    });
  };

  const pickImageFromCamera = () => {
    ImagePicker.openCamera({
      width: 800,
      height: 600,
      cropping: false,
      mediaType: 'photo',
    }).then(async (image) => {
      await sendImageMessage(image);
    }).catch((error) => {
      if (error.code !== 'E_PICKER_CANCELLED') {
        console.error('Camera error:', error);
      }
    });
  };

  const sendImageMessage = async (image: any) => {
    try {
      // Upload image first
      const imageUrl = await ChatAPI.uploadChatFile(image, 'image');
      
      // Create message with image
      const message: IMessage = {
        _id: Date.now().toString(),
        text: '',
        createdAt: new Date(),
        user: {
          _id: customer?.id || '',
          name: customer?.name || '',
          avatar: customer?.avatar,
        },
        image: imageUrl,
      };
      
      onSend([message]);
    } catch (error) {
      console.error('Error sending image:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể gửi ảnh',
      });
    }
  };

  const handleFilePicker = async () => {
    // Tạm thời disable file picker do vấn đề tương thích
    showSnackbar({
      status: ComponentStatus.INFO,
      message: 'Tính năng gửi file sẽ được cập nhật trong phiên bản tiếp theo',
    });

    // TODO: Implement file picker with alternative solution
    // Options:
    // 1. Use react-native-image-picker for images/videos
    // 2. Use custom file browser
    // 3. Wait for react-native-document-picker update
  };

  const renderInputToolbar = (props: any) => (
    <InputToolbar
      {...props}
      containerStyle={styles.inputToolbar}
      primaryStyle={styles.inputPrimary}
    />
  );

  const renderSend = (props: any) => (
    <Send {...props} containerStyle={styles.sendContainer}>
      <Text style={styles.sendText}>Gửi</Text>
    </Send>
  );

  const renderBubble = (props: any) => (
    <Bubble
      {...props}
      wrapperStyle={{
        right: styles.bubbleRight,
        left: styles.bubbleLeft,
      }}
      textStyle={{
        right: styles.textRight,
        left: styles.textLeft,
      }}
    />
  );

  const handleEmojiSelect = (emoji: string) => {
    setInputText(prev => prev + emoji);
  };

  const renderActions = () => (
    <View style={styles.actionsContainer}>
      <TouchableOpacity style={styles.actionButton} onPress={handleImagePicker}>
        <Text style={styles.actionText}>📷</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.actionButton} onPress={handleFilePicker}>
        <Text style={styles.actionText}>📎</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => setShowEmojiPicker(true)}
      >
        <Text style={styles.actionText}>😊</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <GiftedChat
        messages={giftedChatMessages}
        onSend={onSend}
        user={{
          _id: customer?.id || '',
          name: customer?.name || '',
          avatar: customer?.avatar,
        }}
        renderInputToolbar={renderInputToolbar}
        renderSend={renderSend}
        renderBubble={renderBubble}
        loadEarlier={hasMoreMessages}
        onLoadEarlier={onLoadEarlier}
        isLoadingEarlier={chatState.loading}
        placeholder="Nhập tin nhắn..."
        alwaysShowSend
        scrollToBottom
        scrollToBottomComponent={() => (
          <View style={styles.scrollToBottomStyle}>
            <Text style={styles.scrollToBottomText}>↓</Text>
          </View>
        )}
        text={inputText}
        onInputTextChanged={setInputText}
      />
      {renderActions()}

      <EmojiPicker
        visible={showEmojiPicker}
        onClose={() => setShowEmojiPicker(false)}
        onEmojiSelect={handleEmojiSelect}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  inputToolbar: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_border_color,
    paddingHorizontal: 8,
  },
  inputPrimary: {
    alignItems: 'center',
  },
  sendContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginBottom: 8,
  },
  sendText: {
    color: ColorThemes.light.primary_color,
    fontWeight: '600',
    fontSize: 16,
  },
  bubbleRight: {
    backgroundColor: ColorThemes.light.primary_color,
  },
  bubbleLeft: {
    backgroundColor: 'white',
  },
  textRight: {
    color: 'white',
  },
  textLeft: {
    color: ColorThemes.light.neutral_text_color,
  },
  actionsContainer: {
    position: 'absolute',
    bottom: 60,
    left: 8,
    flexDirection: 'row',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  actionText: {
    fontSize: 18,
  },
  scrollToBottomStyle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  scrollToBottomText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ChatRoomScreen;
