import React from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import ConfigAPI from '../../../../Config/ConfigAPI';

interface Category {
  Id: string;
  Name: string;
  Img: string;
}

const CategoryTabsSkeleton = () => (
  <SkeletonPlaceholder backgroundColor="#F0F0F0" highlightColor="#E0E0E0">
    <View style={{flexDirection: 'row', gap: 12}}>
      {Array(5)
        .fill(0)
        .map((_, index) => (
          <View key={index} style={{alignItems: 'center', gap: 4}}>
            <SkeletonPlaceholder.Item
              width={30}
              height={30}
              borderRadius={15}
            />
            <SkeletonPlaceholder.Item width={60} height={12} borderRadius={4} />
          </View>
        ))}
    </View>
  </SkeletonPlaceholder>
);

interface CategoryTabsProps {
  categories: Category[];
  selectedCategory: string;
  onSelectCategory: (id: string) => void;
  scrollViewRef: React.Ref<ScrollView>;
}

const CategoryTabs = ({
  categories,
  selectedCategory,
  onSelectCategory,
  scrollViewRef,
}: CategoryTabsProps) => (
  <ScrollView
    ref={scrollViewRef}
    horizontal
    showsHorizontalScrollIndicator={false}
    style={styles.categoryTabsContainer}
    contentContainerStyle={styles.categoryTabsContent}>
    {categories.length === 0 ? (
      <CategoryTabsSkeleton />
    ) : (
      categories.map(category => (
        <TouchableOpacity
          key={category.Id}
          style={[
            styles.categoryTab,
            selectedCategory === category.Id && styles.activeCategoryTab,
          ]}
          onPress={() => onSelectCategory(category.Id)}>
          <FastImage
            source={{uri: ConfigAPI.urlImg + category.Img}}
            style={{width: 30, height: 30}}
            resizeMode={FastImage.resizeMode.contain}
          />
          <Text
            style={[
              styles.categoryTabText,
              selectedCategory === category.Id && styles.activeCategoryTabText,
            ]}>
            {category.Name}
          </Text>
        </TouchableOpacity>
      ))
    )}
  </ScrollView>
);

const styles = StyleSheet.create({
  categoryTabsContainer: {
    maxHeight: 73,
    marginTop: 16,
  },
  categoryTabsContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    gap: 4,
    borderRadius: 8,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0.5,
    borderColor: '#4EC6EA',
    overflow: 'hidden',
  },
  activeCategoryTab: {
    backgroundColor: '#E3F2FD',
  },
  categoryTabText: {
    ...TypoSkin.buttonText6,
  },
  activeCategoryTabText: {
    color: '#2962FF',
  },
});

export default React.memo(CategoryTabs);
