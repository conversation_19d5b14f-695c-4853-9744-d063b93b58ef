/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {StyleSheet, View, Image} from 'react-native';

const HeaderBackground = () => {
  return (
    <View style={styles.header}>
      {/* Header */}
      <Image
        source={require('../../assets/images/header_group.png')}
        style={styles.headerImage}
      />
    </View>
  );
};
export default HeaderBackground;

const styles = StyleSheet.create({
  header: {
    maxHeight: 120,
  },
  headerImage: {width: '100%', height: 120},
});
