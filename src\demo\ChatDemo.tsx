import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { ColorThemes } from '../assets/skin/colors';
import { navigate, RootScreen } from '../router/router';
import { 
  fetchChatRooms, 
  setChatRooms, 
  addMessage,
  setMessages 
} from '../redux/reducers/ChatReducer';
import { useChatRooms, useChatMessages } from '../redux/hook/chatHook';
import { mockChatRooms, mockMessages } from '../mock/chatData';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';

const ChatDemo: React.FC = () => {
  const dispatch = useDispatch<any>();
  const chatRooms = useChatRooms();

  useEffect(() => {
    // Load mock data when component mounts
    loadMockData();
  }, []);

  const loadMockData = () => {
    try {
      // Load mock chat rooms
      dispatch(setChatRooms(mockChatRooms));
      
      // Load mock messages for each room
      Object.keys(mockMessages).forEach(roomId => {
        dispatch(setMessages({ 
          roomId, 
          messages: mockMessages[roomId] 
        }));
      });

      showSnackbar({
        status: ComponentStatus.SUCCESS,
        message: 'Đã tải dữ liệu demo thành công',
      });
    } catch (error) {
      console.error('Error loading mock data:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tải dữ liệu demo',
      });
    }
  };

  const navigateToChatList = () => {
    navigate(RootScreen.ChatList);
  };

  const navigateToCreateGroup = () => {
    navigate(RootScreen.CreateGroup);
  };

  const navigateToChatRoom = (roomId: string) => {
    const room = mockChatRooms.find(r => r.id === roomId);
    if (room) {
      navigate(RootScreen.ChatRoom, { room });
    }
  };

  const addTestMessage = () => {
    const testMessage = {
      _id: Date.now().toString(),
      text: `Test message ${new Date().toLocaleTimeString()}`,
      createdAt: new Date(),
      user: {
        _id: 'current_user',
        name: 'Bạn',
        avatar: undefined,
      },
      sent: true,
      received: true,
    };

    // Add to first room
    if (mockChatRooms.length > 0) {
      dispatch(addMessage({ 
        roomId: mockChatRooms[0].id, 
        message: testMessage 
      }));
      
      showSnackbar({
        status: ComponentStatus.SUCCESS,
        message: 'Đã thêm tin nhắn test',
      });
    }
  };

  const clearChatData = () => {
    Alert.alert(
      'Xóa dữ liệu chat',
      'Bạn có chắc muốn xóa tất cả dữ liệu chat demo?',
      [
        { text: 'Hủy', style: 'cancel' },
        { 
          text: 'Xóa', 
          style: 'destructive',
          onPress: () => {
            dispatch(setChatRooms([]));
            showSnackbar({
              status: ComponentStatus.INFO,
              message: 'Đã xóa dữ liệu chat',
            });
          }
        },
      ]
    );
  };

  const renderChatRoomItem = (room: any, index: number) => (
    <TouchableOpacity
      key={room.id}
      style={styles.roomItem}
      onPress={() => navigateToChatRoom(room.id)}
      activeOpacity={0.7}
    >
      <View style={styles.roomInfo}>
        <Text style={styles.roomName}>{room.name}</Text>
        <Text style={styles.roomType}>
          {room.type === 'group' ? 'Nhóm' : 'Cá nhân'} • {room.participants.length} thành viên
        </Text>
        {room.lastMessage && (
          <Text style={styles.lastMessage} numberOfLines={1}>
            {room.lastMessage.text}
          </Text>
        )}
      </View>
      {room.unreadCount > 0 && (
        <View style={styles.unreadBadge}>
          <Text style={styles.unreadText}>{room.unreadCount}</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>Chat Demo</Text>
        <Text style={styles.subtitle}>
          Test chức năng chat với dữ liệu mẫu
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Điều hướng</Text>
        
        <TouchableOpacity style={styles.button} onPress={navigateToChatList}>
          <Text style={styles.buttonText}>Mở danh sách chat</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={navigateToCreateGroup}>
          <Text style={styles.buttonText}>Tạo nhóm chat</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Dữ liệu demo</Text>
        
        <TouchableOpacity style={styles.button} onPress={loadMockData}>
          <Text style={styles.buttonText}>Tải lại dữ liệu demo</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={addTestMessage}>
          <Text style={styles.buttonText}>Thêm tin nhắn test</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.dangerButton]} 
          onPress={clearChatData}
        >
          <Text style={[styles.buttonText, styles.dangerButtonText]}>
            Xóa dữ liệu chat
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Phòng chat ({chatRooms.length})
        </Text>
        
        {chatRooms.length === 0 ? (
          <Text style={styles.emptyText}>
            Chưa có phòng chat nào. Nhấn "Tải lại dữ liệu demo" để thêm dữ liệu mẫu.
          </Text>
        ) : (
          chatRooms.map((room, index) => renderChatRoomItem(room, index))
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Thông tin</Text>
        <Text style={styles.infoText}>
          • Dữ liệu demo bao gồm 4 phòng chat (2 cá nhân, 2 nhóm)
        </Text>
        <Text style={styles.infoText}>
          • Mỗi phòng có tin nhắn mẫu và số lượng chưa đọc
        </Text>
        <Text style={styles.infoText}>
          • Socket.IO sẽ được mô phỏng trong môi trường thực
        </Text>
        <Text style={styles.infoText}>
          • Chức năng gửi ảnh, file, emoji đã được tích hợp
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 12,
  },
  button: {
    backgroundColor: ColorThemes.light.primary_color,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  dangerButton: {
    backgroundColor: ColorThemes.light.error_color,
  },
  dangerButtonText: {
    color: 'white',
  },
  roomItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 8,
    marginBottom: 8,
  },
  roomInfo: {
    flex: 1,
  },
  roomName: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 4,
  },
  roomType: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    marginBottom: 4,
  },
  lastMessage: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  unreadBadge: {
    backgroundColor: ColorThemes.light.error_color,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 16,
  },
  infoText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    marginBottom: 4,
    lineHeight: 20,
  },
});

export default ChatDemo;
