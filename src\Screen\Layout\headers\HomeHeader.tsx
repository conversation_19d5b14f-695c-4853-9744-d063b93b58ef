import React, {useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
  Dimensions,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {AppSvg, FDialog, ListTile, Winicon} from 'wini-mobile-components';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../../router/router';
import LinearGradient from 'react-native-linear-gradient';
import iconSvg from '../../../svg/icon';
import CartIcon from '../../../components/CartIcon';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {ColorThemes} from '../../../assets/skin/colors';
import ConfigAPI from '../../../Config/ConfigAPI';
import Svg, {Path} from 'react-native-svg';
import ScreenHeader from '../header';
import {dialogCheckAcc} from '../mainLayout';
import {TypoSkin} from '../../../assets/skin/typography';
import FastImage from 'react-native-fast-image';
import HeaderBackground from '../../../components/shop/HeaderShop';

const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;
const SCREEN_HEIGHT = Dimensions.get('window').height;

interface HomeHeaderProps {
  onSearchPress?: () => void;
  notificationCount?: number;
}

const HomeHeader: React.FC<HomeHeaderProps> = ({
  onSearchPress,
  notificationCount = 5,
}) => {
  const navigation = useNavigation<any>();

  // Thiết lập StatusBar khi component mount
  useEffect(() => {
    // Đảm bảo StatusBar trong suốt và kịch lên đỉnh màn hình
    if (Platform.OS === 'android') {
      StatusBar.setTranslucent(true);
      StatusBar.setBackgroundColor('transparent');
    }
    StatusBar.setBarStyle('dark-content');

    // Trả về hàm cleanup để reset StatusBar khi unmount
    return () => {
      if (Platform.OS === 'android') {
        StatusBar.setTranslucent(false);
        StatusBar.setBackgroundColor('#FFA500');
      }
    };
  }, []);

  const customer = useSelectorCustomerState().data;
  const dialogRef = React.useRef<any>(null);

  return (
    <View style={styles.header}>
      <FDialog ref={dialogRef} />
      {/* Background màu xanh */}
      <View style={styles.headerBackground}>
        <HeaderBackground />
      </View>

      {/* Nội dung header */}
      <SafeAreaView style={styles.headerContent}>
        <ListTile
          leading={
            customer?.AvatarUrl ? (
              <FastImage
                key={customer?.AvatarUrl}
                source={
                  customer?.AvatarUrl
                    ? {
                        uri: customer?.AvatarUrl.includes('https')
                          ? customer?.AvatarUrl
                          : ConfigAPI.urlImg + customer?.AvatarUrl,
                      }
                    : require('../../assets/appstore.png')
                }
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 50,
                  backgroundColor: '#f0f0f0',
                  borderColor: ColorThemes.light.neutral_main_border_color,
                  borderWidth: 1,
                }}
              />
            ) : (
              <View
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 50,
                  backgroundColor: ColorThemes.light.primary_main_color,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                {customer?.Name ? (
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color:
                        ColorThemes.light.neutral_absolute_background_color,
                    }}>
                    {customer?.Name
                      ? customer?.Name.charAt(0).toUpperCase()
                      : ''}
                  </Text>
                ) : (
                  <Image
                    source={require('../../assets/images/logo.png')}
                    style={{width: '100%', height: '100%', borderRadius: 100}}
                  />
                )}
              </View>
            )
          }
          title={`Xin chào, ${customer?.Name ?? 'Người dùng'}`}
          style={{
            width: '100%',
            padding: 0,
            paddingHorizontal: 16,
            backgroundColor: ColorThemes.light.transparent,
          }}
          trailing={
            <View style={styles.rightIcons}>
              {/* Cart icon */}

              {/* User profile icon */}
              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => {
                  if (!customer?.Id) {
                    dialogCheckAcc(dialogRef);
                    return;
                  }
                  // navigate(RootScreen.Notification);
                  navigate(RootScreen.FavoriteProduct);
                }}>
                <View style={styles.iconCircle}>
                  <AppSvg SvgSrc={iconSvg.notification} size={16} />
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => {
                  if (!customer?.Id) {
                    dialogCheckAcc(dialogRef);
                    return;
                  }
                  navigate(RootScreen.CartPage);
                }}>
                <View style={styles.iconCircle}>
                  <CartIcon isHome color="#0033CC" size={18} showBadge={true} />
                </View>
              </TouchableOpacity>
            </View>
          }
          bottom={
            <View style={styles.searchContainer}>
              <TouchableOpacity
                style={styles.searchBar}
                onPress={onSearchPress}
                activeOpacity={0.8}>
                <Winicon
                  src="outline/user interface/search"
                  size={18}
                  color="#999"
                />
                <Text style={styles.searchPlaceholder}>Bạn muốn tìm gì?</Text>
              </TouchableOpacity>
              {/* <TouchableOpacity style={styles.filterButton}>
                <AppSvg SvgSrc={iconSvg.filter} size={24} />
              </TouchableOpacity> */}
            </View>
          }
        />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    width: '100%',
    position: 'relative',
    paddingTop: 16,
    marginTop: 0,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 2,
    overflow: 'hidden',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    zIndex: 2,
    left: 0,
    right: 0,
  },

  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  wavyBottom: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 30,
    overflow: 'hidden',
  },
  wavyCurve: {
    position: 'absolute',
    bottom: -30,
    left: 0,
    right: 0,
    height: 60,
    backgroundColor: 'white',
    borderTopLeftRadius: 100,
    borderTopRightRadius: 100,
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  menuButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 8,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: 'red',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 11,
    paddingHorizontal: 16,
    paddingVertical: 8,
    height: 36,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
  },
  searchPlaceholder: {
    flex: 1,
    marginLeft: 8,
    color: '#999',
    fontSize: 14,
  },
  filterButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default HomeHeader;
