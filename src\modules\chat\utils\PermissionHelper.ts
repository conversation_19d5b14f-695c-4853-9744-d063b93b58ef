import { Platform, PermissionsAndroid, Alert } from 'react-native';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

export class PermissionHelper {
  
  // Kiểm tra và yêu cầu quyền camera
  static async requestCameraPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Quyền truy cập Camera',
            message: 'Ứng dụng cần quyền truy cập camera để chụp ảnh',
            buttonNeutral: 'Hỏi lại sau',
            buttonNegative: 'Từ chối',
            buttonPositive: 'Đồng ý',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('Camera permission error:', err);
        return false;
      }
    }
    return true; // iOS permissions are handled by Info.plist
  }

  // Kiểm tra và yêu cầu quyền thư viện ảnh
  static async requestPhotoLibraryPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          {
            title: 'Quyền truy cập Thư viện ảnh',
            message: 'Ứng dụng cần quyền truy cập thư viện ảnh để chọn ảnh',
            buttonNeutral: 'Hỏi lại sau',
            buttonNegative: 'Từ chối',
            buttonPositive: 'Đồng ý',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('Photo library permission error:', err);
        return false;
      }
    }
    return true; // iOS permissions are handled by Info.plist
  }

  // Kiểm tra và yêu cầu quyền đọc file
  static async requestStoragePermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        // Android 13+ sử dụng READ_MEDIA_* permissions
        if (Platform.Version >= 33) {
          const permissions = [
            PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
            PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
            PermissionsAndroid.PERMISSIONS.READ_MEDIA_AUDIO,
          ];
          
          const results = await PermissionsAndroid.requestMultiple(permissions);
          
          return Object.values(results).some(
            result => result === PermissionsAndroid.RESULTS.GRANTED
          );
        } else {
          // Android < 13 sử dụng READ_EXTERNAL_STORAGE
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
            {
              title: 'Quyền truy cập File',
              message: 'Ứng dụng cần quyền truy cập file để chọn và gửi file',
              buttonNeutral: 'Hỏi lại sau',
              buttonNegative: 'Từ chối',
              buttonPositive: 'Đồng ý',
            }
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
      } catch (err) {
        console.warn('Storage permission error:', err);
        return false;
      }
    }
    return true; // iOS permissions are handled by Info.plist
  }

  // Hiển thị dialog khi không có quyền
  static showPermissionDeniedAlert(permissionType: string) {
    Alert.alert(
      'Không có quyền truy cập',
      `Ứng dụng cần quyền ${permissionType} để thực hiện chức năng này. Vui lòng cấp quyền trong Cài đặt.`,
      [
        { text: 'Hủy', style: 'cancel' },
        { text: 'Mở Cài đặt', onPress: () => {
          // Có thể sử dụng Linking.openSettings() nếu cần
        }},
      ]
    );
  }

  // Kiểm tra tất cả quyền cần thiết cho chat
  static async checkAllChatPermissions(): Promise<{
    camera: boolean;
    photoLibrary: boolean;
    storage: boolean;
  }> {
    const [camera, photoLibrary, storage] = await Promise.all([
      this.requestCameraPermission(),
      this.requestPhotoLibraryPermission(),
      this.requestStoragePermission(),
    ]);

    return {
      camera,
      photoLibrary,
      storage,
    };
  }
}

// Utility function để kiểm tra xem thư viện có hoạt động không
export const testLibraries = () => {
  console.log('Testing chat libraries...');
  
  try {
    // Test react-native-gifted-chat
    const { GiftedChat } = require('react-native-gifted-chat');
    console.log('✅ react-native-gifted-chat loaded successfully');
    
    // Test socket.io-client
    const io = require('socket.io-client');
    console.log('✅ socket.io-client loaded successfully');
    
    // Test react-native-image-crop-picker
    const ImagePicker = require('react-native-image-crop-picker');
    console.log('✅ react-native-image-crop-picker loaded successfully');
    
    // Test react-native-document-picker
    const DocumentPicker = require('react-native-document-picker');
    console.log('✅ react-native-document-picker loaded successfully');
    
    console.log('🎉 All chat libraries loaded successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error loading chat libraries:', error);
    return false;
  }
};
