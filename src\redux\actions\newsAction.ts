import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {NewsItem} from '../models/news';
import {BaseDA} from '../../base/BaseDA';
import ConfigAPI from '../../Config/ConfigAPI';
import {getImage} from './rootAction';

const fetchNews = createAsyncThunk<
  NewsItem[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('news/fetchNews', async (config, thunkAPI: any) => {
  const controller = new DataController('News');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 2,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200) {
      const listData = await getImage({items: res.data});
      return {
        data: listData,
        totalCount: res.totalCount,
      };
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

const loadMoreNews = createAsyncThunk<
  NewsItem[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('news/fetchData', async (config, thunkAPI: any) => {
  const controller = new DataController('News');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 2,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200) {
      const listData = await getImage({items: res.data});
      return listData;
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export {fetchNews, loadMoreNews};
