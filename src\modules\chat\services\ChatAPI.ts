import { BaseDA } from '../../../base/BaseDA';
import { DataController } from '../../../base/baseController';
import ConfigAPI from '../../../Config/ConfigAPI';
import { ChatRoom, ChatMessage, CreateGroupRequest, SendMessageRequest } from '../types/ChatTypes';
import { getMockChatRooms, getMockMessages, getMockUsers } from '../../../mock/chatData';

class ChatAPI {
  private chatController: DataController;
  private messageController: DataController;
  private useMockData: boolean = true; // Set to false when real API is ready

  constructor() {
    this.chatController = new DataController('ChatRoom');
    this.messageController = new DataController('ChatMessage');
  }

  // Lấy danh sách phòng chat
  async getChatRooms(page: number = 1, size: number = 20): Promise<{ data: ChatRoom[]; total: number }> {
    if (this.useMockData) {
      return getMockChatRooms();
    }

    try {
      const response = await this.chatController.aggregateList({
        page,
        size,
        sortby: [{ prop: 'updatedAt', direction: 'DESC' }],
      });
      return {
        data: response.data || [],
        total: response.total || 0,
      };
    } catch (error) {
      console.error('Error fetching chat rooms:', error);
      throw error;
    }
  }

  // Lấy tin nhắn trong phòng chat
  async getMessages(roomId: string, page: number = 1, size: number = 50): Promise<{ data: ChatMessage[]; total: number }> {
    if (this.useMockData) {
      return getMockMessages(roomId);
    }

    try {
      const response = await this.messageController.aggregateList({
        page,
        size,
        filter: JSON.stringify({ roomId }),
        sortby: [{ prop: 'createdAt', direction: 'DESC' }],
      });
      return {
        data: response.data || [],
        total: response.total || 0,
      };
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  }

  // Tạo phòng chat 1-1
  async createPrivateRoom(participantId: string): Promise<ChatRoom> {
    try {
      const response = await BaseDA.post(ConfigAPI.url + 'chat/create-private-room', {
        body: { participantId },
      });
      return response.data;
    } catch (error) {
      console.error('Error creating private room:', error);
      throw error;
    }
  }

  // Tạo nhóm chat
  async createGroupRoom(groupData: CreateGroupRequest): Promise<ChatRoom> {
    try {
      const response = await BaseDA.post(ConfigAPI.url + 'chat/create-group-room', {
        body: groupData,
      });
      return response.data;
    } catch (error) {
      console.error('Error creating group room:', error);
      throw error;
    }
  }

  // Gửi tin nhắn
  async sendMessage(messageData: SendMessageRequest): Promise<ChatMessage> {
    try {
      const response = await BaseDA.post(ConfigAPI.url + 'chat/send-message', {
        body: messageData,
      });
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Upload file/image cho chat
  async uploadChatFile(file: any, type: 'image' | 'video' | 'audio' | 'file'): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      const response = await BaseDA.post(ConfigAPI.url + 'chat/upload-file', {
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data.url;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  // Tìm kiếm người dùng để thêm vào chat
  async searchUsers(query: string): Promise<any[]> {
    if (this.useMockData) {
      return getMockUsers(query);
    }

    try {
      const userController = new DataController('Customer');
      const response = await userController.aggregateList({
        searchRaw: query,
        size: 20,
      });
      return response.data || [];
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  // Đánh dấu tin nhắn đã đọc
  async markAsRead(roomId: string): Promise<void> {
    try {
      await BaseDA.post(ConfigAPI.url + 'chat/mark-as-read', {
        body: { roomId },
      });
    } catch (error) {
      console.error('Error marking as read:', error);
      throw error;
    }
  }

  // Rời khỏi nhóm chat
  async leaveGroup(roomId: string): Promise<void> {
    try {
      await BaseDA.post(ConfigAPI.url + 'chat/leave-group', {
        body: { roomId },
      });
    } catch (error) {
      console.error('Error leaving group:', error);
      throw error;
    }
  }

  // Thêm thành viên vào nhóm
  async addMembersToGroup(roomId: string, memberIds: string[]): Promise<void> {
    try {
      await BaseDA.post(ConfigAPI.url + 'chat/add-members', {
        body: { roomId, memberIds },
      });
    } catch (error) {
      console.error('Error adding members:', error);
      throw error;
    }
  }
}

export default new ChatAPI();
