import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ScrollView,
  Alert,
  ImageBackground,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {
  AppSvg,
  ComponentStatus,
  FDialog,
  FLoading,
  showDialog,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {navigate, RootScreen} from '../../router/router';
import HeaderShop from '../../components/shop/HeaderShop';
import GiftCard from '../../components/Gift/GiftCard';
import ExchangeHistoryCard from '../../components/Gift/ExchangeHistoryCard';
import {
  GiftCardSkeleton,
  ExchangeHistoryCardSkeleton,
} from '../../components/Gift/GiftSkeleton';
import GiftDA, {GiftItem, ExchangeGiftItem} from '../../modules/gift/giftDA';
import {Ultis} from '../../utils/Utils';
import iconSvg from '../../svg/icon';
import GiftCardExchange from '../../components/Gift/ExchangeHistoryCard';
import {InforHeader} from '../Layout/headers/inforHeader';

const PAGE_SIZE = 10;

const GiftExchange = () => {
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;
  const giftDA = new GiftDA();
  const dialogRef = useRef<any>(null);
  // States
  const [activeTab, setActiveTab] = useState<'gifts' | 'pending' | 'history'>(
    'gifts',
  );
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  // Gifts tab states
  const [gifts, setGifts] = useState<GiftItem[]>([]);
  const [giftsPage, setGiftsPage] = useState(1);
  const [giftsHasMore, setGiftsHasMore] = useState(true);

  // Pending tab states
  const [pendingExchanges, setPendingExchanges] = useState<ExchangeGiftItem[]>(
    [],
  );
  const [pendingPage, setPendingPage] = useState(1);
  const [pendingHasMore, setPendingHasMore] = useState(true);

  // History tab states
  const [exchangeHistory, setExchangeHistory] = useState<ExchangeGiftItem[]>(
    [],
  );
  const [selectedStatus, setSelectedStatus] = useState<number | undefined>(
    undefined,
  );
  const [historyPage, setHistoryPage] = useState(1);
  const [historyHasMore, setHistoryHasMore] = useState(true);

  // Pending count for tab badge
  const [pendingCount, setPendingCount] = useState(0);

  // Current points
  const [currentPoints, setCurrentPoints] = useState(0);

  // Fetch current points
  const fetchCurrentPoints = async () => {
    if (!customer?.Id) return;
    try {
      const points = await giftDA.getCurrentPoints(customer.Id);
      setCurrentPoints(points);
    } catch (error) {
      console.error('Error fetching current points:', error);
    }
  };

  // Fetch gifts
  const fetchGifts = async (
    page: number = 1,
    isLoadMore: boolean = false,
    isRefresh: boolean = false,
  ) => {
    if (!customer?.Id) return;

    if (isRefresh) {
      setRefreshing(true);
    } else if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const response = await giftDA.getGifts(page, PAGE_SIZE);

      if (response?.code === 200) {
        const newGifts = response.data || [];

        if (isLoadMore) {
          setGifts(prev => [...prev, ...newGifts]);
        } else {
          setGifts(newGifts);
        }

        setGiftsHasMore(newGifts.length === PAGE_SIZE);
        setGiftsPage(page);
      } else {
        showSnackbar({
          message: 'Không thể tải danh sách quà tặng',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error fetching gifts:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải danh sách quà tặng',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  // Fetch pending exchanges
  const fetchPendingExchanges = async (
    page: number = 1,
    isLoadMore: boolean = false,
    isRefresh: boolean = false,
  ) => {
    if (!customer?.Id) return;

    if (isRefresh) {
      setRefreshing(true);
    } else if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const response = await giftDA.getExchangeHistory(
        customer.Id,
        page,
        PAGE_SIZE,
        0, // Status = 0 for pending
      );

      if (response?.code === 200) {
        const newPending = response.data || [];
        //map ảnh và tên
        var gift = response.Gifts;
        newPending.forEach((item: any) => {
          item.Name = gift.find((gift: any) => gift.Id === item.GiftsId).Name;
          item.Img = gift.find((gift: any) => gift.Id === item.GiftsId).Img;
        });
        if (isLoadMore) {
          setPendingExchanges(prev => [...prev, ...newPending]);
        } else {
          setPendingExchanges(newPending);
          setPendingCount(response.totalCount || newPending.length);
        }

        setPendingHasMore(newPending.length === PAGE_SIZE);
        setPendingPage(page);
      } else {
        showSnackbar({
          message: 'Không thể tải danh sách chờ duyệt',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error fetching pending exchanges:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải danh sách chờ duyệt',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  // Fetch exchange history
  const fetchExchangeHistory = async (
    page: number = 1,
    isLoadMore: boolean = false,
    isRefresh: boolean = false,
  ) => {
    if (!customer?.Id) return;

    if (isRefresh) {
      setRefreshing(true);
    } else if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const response = await giftDA.getExchangeHistory(
        customer.Id,
        page,
        PAGE_SIZE,
        selectedStatus,
      );

      if (response?.code === 200) {
        const newHistory = response.data || [];
        var gift = response.Gifts;
        newHistory.forEach((item: any) => {
          item.Name = gift.find((gift: any) => gift.Id === item.GiftsId).Name;
          item.Img = gift.find((gift: any) => gift.Id === item.GiftsId).Img;
        });
        if (isLoadMore) {
          setExchangeHistory(prev => [...prev, ...newHistory]);
        } else {
          setExchangeHistory(newHistory);
        }

        setHistoryHasMore(newHistory.length === PAGE_SIZE);
        setHistoryPage(page);
      } else {
        showSnackbar({
          message: 'Không thể tải lịch sử đổi quà',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error fetching exchange history:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải lịch sử đổi quà',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  const TabItem = ({
    label,
    isActive,
    onPress,
    icon,
    type,
  }: {
    label: string;
    isActive: boolean;
    onPress: () => void;
    icon: any;
    type: string;
  }) => {
    return isActive ? (
      <LinearGradient
        colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        style={{...styles.tab, width: type === 'pending' ? '40%' : '30%'}}>
        <AppSvg SvgSrc={icon} size={20} style={{marginRight: 4}} />
        <Text style={[styles.activeTabText]}>{label}</Text>
      </LinearGradient>
    ) : (
      <TouchableOpacity
        style={{...styles.tab, width: type === 'pending' ? '40%' : '30%'}}
        onPress={onPress}>
        <AppSvg SvgSrc={icon} size={20} style={{marginRight: 4}} />
        <Text style={[styles.tabText]}>{label}</Text>
      </TouchableOpacity>
    );
  };

  // Handle exchange gift
  const handleExchangeGift = async (gift: GiftItem) => {
    if (!customer?.Id) return;

    if (currentPoints < gift.Value) {
      showSnackbar({
        message: 'Số điểm không đủ để đổi quà này',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    showDialog({
      ref: dialogRef,
      title: 'Xác nhận đổi quà',
      content: `Bạn có chắc chắn muốn đổi "${gift.Name}" với ${Ultis.money(
        gift.Value,
      )} điểm?`,
      onSubmit: async () => {
        try {
          setLoading(true);
          const response = await giftDA.exchangeGift(
            gift.Id!,
            customer.Id,
            gift.Value,
            gift.Name!,
          );

          if (response?.code === 200) {
            showSnackbar({
              message: 'Đổi quà thành công! Vui lòng chờ duyệt.',
              status: ComponentStatus.SUCCSESS,
            });

            //trừ điểm
            setCurrentPoints(currentPoints - gift.Value);
            if (activeTab === 'gifts') {
              fetchGifts(1, false, false);
            } else if (activeTab === 'pending') {
              fetchPendingExchanges(1, false, false);
            } else {
              fetchExchangeHistory(1, false, false);
            }
          } else {
            showSnackbar({
              message: 'Không thể đổi quà. Vui lòng thử lại.',
              status: ComponentStatus.ERROR,
            });
          }
        } catch (error) {
          console.error('Error exchanging gift:', error);
          showSnackbar({
            message: 'Có lỗi xảy ra khi đổi quà',
            status: ComponentStatus.ERROR,
          });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // Handle gift press
  const handleGiftPress = (gift: GiftItem) => {
    navigate(RootScreen.GiftDetail, {giftId: gift.Id});
  };

  // Handle tab change
  const handleTabChange = (tab: 'gifts' | 'pending' | 'history') => {
    setActiveTab(tab);
    if (tab === 'gifts') {
      if (gifts.length === 0) {
        fetchGifts();
      }
    } else if (tab === 'pending') {
      if (pendingExchanges.length === 0) {
        fetchPendingExchanges();
      }
    } else {
      if (exchangeHistory.length === 0) {
        fetchExchangeHistory();
      }
    }
  };

  // Handle status filter
  const handleStatusFilter = (status: number | undefined) => {
    setSelectedStatus(status);
    setExchangeHistory([]);
    setHistoryPage(1);
    setHistoryHasMore(true);
  };

  // Load more data
  const handleLoadMore = () => {
    if (activeTab === 'gifts' && giftsHasMore && !loadingMore) {
      fetchGifts(giftsPage + 1, true, false);
    } else if (activeTab === 'pending' && pendingHasMore && !loadingMore) {
      fetchPendingExchanges(pendingPage + 1, true, false);
    } else if (activeTab === 'history' && historyHasMore && !loadingMore) {
      fetchExchangeHistory(historyPage + 1, true, false);
    }
  };

  // Refresh data
  const handleRefresh = () => {
    fetchCurrentPoints();
    if (activeTab === 'gifts') {
      setGifts([]);
      setGiftsPage(1);
      setGiftsHasMore(true);
      fetchGifts(1, false, true);
    } else if (activeTab === 'pending') {
      setPendingExchanges([]);
      setPendingPage(1);
      setPendingHasMore(true);
      fetchPendingExchanges(1, false, true);
    } else {
      setExchangeHistory([]);
      setHistoryPage(1);
      setHistoryHasMore(true);
      fetchExchangeHistory(1, false, true);
    }
  };

  // Initial load and focus effect
  useFocusEffect(
    useCallback(() => {
      if (customer?.Id) {
        fetchCurrentPoints();
        if (activeTab === 'gifts') {
          fetchGifts();
        } else if (activeTab === 'pending') {
          fetchPendingExchanges();
        } else {
          fetchExchangeHistory();
        }
      }
    }, [customer?.Id]),
  );

  // Filter data when category or status changes
  useEffect(() => {
    if (activeTab === 'gifts') {
      setGifts([]);
      setGiftsPage(1);
      setGiftsHasMore(true);
      fetchGifts();
      fetchPendingExchanges();
    }
  }, []);

  useEffect(() => {
    if (activeTab === 'history') {
      setExchangeHistory([]);
      setHistoryPage(1);
      setHistoryHasMore(true);
      fetchExchangeHistory();
    }
  }, [selectedStatus]);

  // Fetch pending count on mount
  useEffect(() => {
    if (customer?.Id) {
      fetchPendingExchanges();
    }
  }, [customer?.Id]);

  return (
    <View style={styles.container}>
      <FLoading visible={loading && !refreshing && !loadingMore} />
      <FDialog ref={dialogRef} />
      {/* Header */}
      <InforHeader title="Đổi quà" />

      {/* Points Display */}
      <View style={styles.pointsContainer}>
        <ImageBackground
          source={require('../../assets/Line-cate.png')}
          imageStyle={{resizeMode: 'contain'}}>
          <View style={styles.pointsContent}>
            <Text style={styles.pointsLabel}>Bạn đang có</Text>
            <View style={styles.pointsIconContainer}>
              <AppSvg SvgSrc={iconSvg.moneyIcon} size={24} />
              <Text style={styles.pointsValue}>
                {Ultis.money(currentPoints)} điểm
              </Text>
            </View>
          </View>
        </ImageBackground>
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <TabItem
          label="Đổi quà"
          isActive={activeTab === 'gifts'}
          onPress={() => handleTabChange('gifts')}
          icon={iconSvg.giftExchange}
          type="gifts"
        />
        <TabItem
          label={`Chờ duyệt (${pendingCount})`}
          isActive={activeTab === 'pending'}
          onPress={() => handleTabChange('pending')}
          icon={iconSvg.audit}
          type="pending"
        />
        <TabItem
          label="Lịch sử"
          isActive={activeTab === 'history'}
          onPress={() => handleTabChange('history')}
          icon={iconSvg.premium}
          type="history"
        />
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'gifts' ? (
          <>
            {' '}
            {/* Gifts List */}
            <FlatList
              data={gifts}
              keyExtractor={(item, index) => `${item.Id}-${index}`}
              renderItem={({item}) => (
                <GiftCard
                  item={item}
                  onPress={handleGiftPress}
                  onExchange={handleExchangeGift}
                  currentPoints={currentPoints}
                />
              )}
              ListEmptyComponent={() =>
                loading ? (
                  <View>
                    {[...Array(3)].map((_, index) => (
                      <GiftCardSkeleton key={index} />
                    ))}
                  </View>
                ) : (
                  <View style={styles.emptyContainer}>
                    <Winicon
                      src="outline/shopping/gift"
                      size={48}
                      color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                    <Text style={styles.emptyText}>Không có quà tặng nào</Text>
                  </View>
                )
              }
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                />
              }
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.1}
              ListFooterComponent={() =>
                loadingMore ? <GiftCardSkeleton /> : null
              }
              showsVerticalScrollIndicator={false}
            />
          </>
        ) : activeTab === 'pending' ? (
          <>
            {/* Pending Exchanges List */}
            <FlatList
              data={pendingExchanges}
              keyExtractor={(item, index) => `${item.Id}-${index}`}
              renderItem={({item}) => <GiftCardExchange item={item} />}
              ListEmptyComponent={() =>
                loading ? (
                  <View>
                    {[...Array(3)].map((_, index) => (
                      <ExchangeHistoryCardSkeleton key={index} />
                    ))}
                  </View>
                ) : (
                  <View style={styles.emptyContainer}>
                    <Winicon
                      src="outline/time/clock"
                      size={48}
                      color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                    <Text style={styles.emptyText}>
                      Không có giao dịch nào đang chờ duyệt
                    </Text>
                  </View>
                )
              }
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                />
              }
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.1}
              ListFooterComponent={() =>
                loadingMore ? <ExchangeHistoryCardSkeleton /> : null
              }
              showsVerticalScrollIndicator={false}
            />
          </>
        ) : (
          <>
            {/* Status Filter */}
            {/* <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.filterContainer}
              contentContainerStyle={styles.filterContent}>
              {[                
                { id: 0, name: 'Đang chờ duyệt' },
                { id: 1, name: 'Đã duyệt' },
                { id: 2, name: 'Từ chối' },
              ].map(status => (
                <TouchableOpacity
                  key={status.id ?? 'all'}
                  style={[
                    styles.filterButton,
                    selectedStatus === status.id && styles.activeFilterButton,
                  ]}
                  onPress={() => handleStatusFilter(status.id)}>
                  <Text
                    style={[
                      styles.filterButtonText,
                      selectedStatus === status.id && styles.activeFilterButtonText,
                    ]}>
                    {status.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView> */}

            {/* Exchange History List */}
            <FlatList
              data={exchangeHistory}
              keyExtractor={(item, index) => `${item.Id}-${index}`}
              renderItem={({item}) => <GiftCardExchange item={item} />}
              ListEmptyComponent={() =>
                loading ? (
                  <View>
                    {[...Array(3)].map((_, index) => (
                      <ExchangeHistoryCardSkeleton key={index} />
                    ))}
                  </View>
                ) : (
                  <View style={styles.emptyContainer}>
                    <Winicon
                      src="outline/time/history"
                      size={48}
                      color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                    <Text style={styles.emptyText}>
                      Chưa có lịch sử đổi quà
                    </Text>
                  </View>
                )
              }
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                />
              }
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.1}
              ListFooterComponent={() =>
                loadingMore ? <ExchangeHistoryCardSkeleton /> : null
              }
              showsVerticalScrollIndicator={false}
            />
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  pointsContainer: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    height: 82,
  },
  pointsContent: {
    width: '100%',
    flexDirection: 'column',
    height: 82,
    paddingHorizontal: 70,
    paddingVertical: 12,
  },
  pointsIconContainer: {
    width: '100%',
    flexDirection: 'row',
    gap: 4,
  },
  pointsLabel: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_title_color,
    fontSize: 16,
    fontWeight: '500',
  },
  pointsValue: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '500',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
  },
  tab: {
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 4,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  tabText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 12,
  },
  activeTabText: {
    color: 'white',
    fontWeight: '600',
  },
  tabTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  tabBadge: {
    backgroundColor: '#FF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  tabBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  filterContainer: {
    // maxHeight: 50,
    marginBottom: 8,
  },
  filterContent: {
    // paddingHorizontal: 16,
    gap: 8,
  },
  filterButton: {
    // paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_border_color,
  },
  activeFilterButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    borderColor: ColorThemes.light.primary_main_color,
  },
  filterButtonText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  activeFilterButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 16,
  },
});

export default GiftExchange;
