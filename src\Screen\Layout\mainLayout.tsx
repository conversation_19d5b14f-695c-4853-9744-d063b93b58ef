import {StyleSheet, View} from 'react-native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {ComponentStatus, FDialog, showDialog} from 'wini-mobile-components';
import Home from '../Page/Home';
import {ColorThemes} from '../../assets/skin/colors';
import {useRoute} from '@react-navigation/native';
import {useRef} from 'react';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import CustomTabBar from '../../components/CustomTabBar';
import {navigateReset, RootScreen} from '../../router/router';
import Shop from '../../modules/shop/ManageShop';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {useDispatch} from 'react-redux';
import ProductListByCategory from '../../modules/Product/list/ProductListByCategory';
import MyWallet from '../Page/myWallet';
import NewsScreen from '../../modules/news/NewsScreen';
import ChatListScreen from '../../modules/chat/screens/ChatListScreen';
const Tab = createBottomTabNavigator();

const bottomNavigateData = [
  {
    id: 0,
    name: 'home',
    component: Home,
  },
  {
    id: 5,
    name: 'news',
    component: NewsScreen,
  },
  {
    id: 1,
    name: 'category',
    component: ProductListByCategory,
  },
  {
    id: 2,
    name: 'QRCode',
    component: MyWallet,
  },
  {
    id: 3,
    name: 'community',
    component: Home,
  },
  {
    id: 6,
    name: 'chat',
    component: ChatListScreen,
  },
  {
    id: 4,
    name: 'profile',
    component: Shop,
  },
];

export const dialogCheckAcc = (ref: any) => {
  showDialog({
    ref: ref,
    status: ComponentStatus.WARNING,
    title: 'Vui lòng đăng nhập để sử dụng!',
    onSubmit: async () => {
      navigateReset(RootScreen.login);
    },
  });
};

export default function EComLayout() {
  const route = useRoute<any>();
  const dialogCheckAccRef = useRef<any>(null);
  const user = useSelectorCustomerState().data;
  const shop = useSelectorShopState().data;
  const dispatch = useDispatch<any>();
  return (
    <View style={styles.container}>
      <FDialog ref={dialogCheckAccRef} />
      <Tab.Navigator
        initialRouteName={route.params?.rootName ?? undefined}
        screenOptions={{
          headerShown: false,
        }}
        tabBar={props => (
          <CustomTabBar props={props} ref={dialogCheckAccRef} />
        )}>
        {bottomNavigateData.map((item, index) => {
          return (
            <Tab.Screen
              listeners={{
                tabPress: (e: any) => {
                  if (
                    !user &&
                    item.name !== 'news' &&
                    item.name !== 'home' &&
                    item.name !== 'profile' &&
                    item.name !== 'category' &&
                    item.name !== 'chat'
                  ) {
                    dialogCheckAcc(dialogCheckAccRef);
                    // Prevent default action
                    e.preventDefault();
                    return;
                  }
                  //Any custom code here
                },
              }}
              key={`${index}`}
              name={item.name}
              options={{
                headerShown: false,
              }}
              component={item.component}
            />
          );
        })}
      </Tab.Navigator>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingBottom: 16,
  },
});
