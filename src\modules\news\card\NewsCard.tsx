import React from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import {NewsItem} from '../../../redux/models/news';
import {formatTimeAgo} from '../../../utils/Utils';
import iconSvg from '../../../svg/icon';
import {AppSvg} from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';

const defaultImage = require('../../../assets/images/default_img.png');

const NewsCard = ({item}: {item: NewsItem}) => {
  const {
    Name = '',
    Description = '',
    relativeUser,
    Likes = 0,
    Img = '',
    Comments = 0,
    DateCreated = 0,
  } = item;

  return (
    <View style={styles.cardContainer}>
      <View style={styles.contentContainer}>
        <View style={styles.textContainer}>
          <View style={styles.header}>
            {/* đoạn này trong data chưa có user */}
            {/* <Image source={{uri: relativeUser.image}} style={styles.avatar} />
            <View style={styles.authorInfo}>
              <Text style={styles.authorName}>{relativeUser.title}</Text>
            </View> */}
            <Text style={styles.timestamp}>{formatTimeAgo(DateCreated)}</Text>
          </View>
          <Text style={styles.title} numberOfLines={2}>
            {Name}
          </Text>
          <Text style={styles.description} numberOfLines={2}>
            {Description}
          </Text>

          {/* Footer */}
          <View style={styles.footer}>
            <View style={styles.statsContainer}>
              <TouchableOpacity style={styles.statItem}>
                <AppSvg SvgSrc={iconSvg.thumbUp} size={16} />
                <Text style={styles.statText}>{Likes}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.statItem}>
                <AppSvg SvgSrc={iconSvg.comment} size={16} />
                <Text style={styles.statText}>{Comments}</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.actionsContainer}>
              <TouchableOpacity style={{marginRight: 16}}>
                <AppSvg SvgSrc={iconSvg.bookmark} size={16} />
              </TouchableOpacity>
              <TouchableOpacity>
                <AppSvg SvgSrc={iconSvg.share} size={16} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <FastImage
          source={Img ? {uri: Img} : defaultImage}
          style={styles.thumbnail}
          resizeMode="cover"
        />
      </View>
    </View>
  );
};

// Styles không thay đổi
const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 8,
    marginHorizontal: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 20,
    height: 20,
    borderRadius: 20,
  },
  authorInfo: {
    marginLeft: 8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  authorName: {
    fontSize: 10,
    color: '#333',
  },
  timestamp: {
    fontSize: 10,
    color: '#888',
    marginLeft: 4,
  },
  contentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111',
    marginBottom: 8,
    lineHeight: 24,
  },
  description: {
    fontSize: 12,
    color: '#666',
    lineHeight: 20,
  },
  thumbnail: {
    width: 130,
    height: '100%',
    borderRadius: 8,
    backgroundColor: '#eee',
  },
  footer: {
    marginTop: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 14,
  },
  statText: {
    marginLeft: 8,
    fontSize: 12,
    color: '#555',
  },
  actionsContainer: {
    flexDirection: 'row',
  },
  actionIcon: {
    marginLeft: 20,
  },
});

export default NewsCard;
