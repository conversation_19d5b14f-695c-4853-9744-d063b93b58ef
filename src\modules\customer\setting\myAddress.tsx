import {useNavigation, useRoute} from '@react-navigation/native';
import {useState, useEffect} from 'react';
import {
  Dimensions,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {ColorThemes} from '../../../assets/skin/colors';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import {DataController} from '../../../base/baseController';
import {AppButton, FLoading, Winicon} from 'wini-mobile-components';
import WScreenFooter from '../../../Screen/Layout/footer';
import {RootScreen} from '../../../router/router';
import {SafeAreaView} from 'react-native-safe-area-context';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {TypoSkin} from '../../../assets/skin/typography';
import {ScrollView} from 'react-native-gesture-handler';
import EmptyPage from '../../../Screen/emptyPage';

export default function MyAddress() {
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const dispatch = useDispatch<any>();
  const [data, setData] = useState<Array<any>>([]);
  const [refresh, setrefresh] = useState(false);
  const now = new Date();
  const route = useRoute<any>();
  const chooseAddress = route.params?.chooseAddress;

  const {myAddress, onLoading} = useSelectorCustomerState();

  useEffect(() => {}, []);

  return (
    <SafeAreaView
      edges={['bottom']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FLoading visible={onLoading} />
      <InforHeader
        title="Địa chỉ nhận hàng"
        onBack={() => navigation.goBack()}
      />
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refresh}
            onRefresh={async () => {
              setrefresh(true);
              await dispatch(CustomerActions.getAddresses(user.Id));
              setrefresh(false);
            }}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        style={{flex: 1}}
        showsVerticalScrollIndicator={false}>
        {myAddress?.length ? (
          myAddress.map((item: any, index: number) => {
            return (
              <TouchableOpacity
                onPress={
                  chooseAddress
                    ? () => {
                        dispatch(
                          CustomerActions.editAddress(
                            {...item, IsDefault: true},
                            false,
                          ),
                        );
                        navigation.goBack();
                      }
                    : undefined
                }
                key={index}
                style={styles.orderInfoRow}>
                <View style={styles.orderInfoContent}>
                  <Text style={styles.orderInfoLabel}>
                    {item?.Name} - {item?.Mobile}
                  </Text>
                  {item?.Email ? (
                    <Text style={styles.orderInfoValue}>{item?.Email}</Text>
                  ) : null}
                  <Text style={styles.orderInfoValue}>{item?.Address}</Text>
                  {item?.IsDefault && (
                    <Text
                      style={{
                        ...TypoSkin.body3,
                        color: ColorThemes.light.primary_main_color,
                        fontWeight: 'bold',
                      }}>
                      Mặc định
                    </Text>
                  )}
                </View>
                <View style={{gap: 8, flexDirection: 'row'}}>
                  <TouchableOpacity
                    style={{padding: 4}}
                    onPress={() => {
                      navigation.push(RootScreen.EditAddress, {
                        item: item,
                      });
                    }}>
                    <Winicon
                      src="outline/user interface/edit"
                      size={20}
                      color={ColorThemes.light.neutral_text_title_color}
                    />
                  </TouchableOpacity>
                  {chooseAddress ? null : (
                    <TouchableOpacity
                      style={{padding: 4}}
                      onPress={() => {
                        dispatch(CustomerActions.deleteAddress(item.Id));
                      }}>
                      <Winicon
                        src="outline/layout/trash"
                        size={20}
                        color={ColorThemes.light.neutral_text_title_color}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </TouchableOpacity>
            );
          })
        ) : (
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              height: Dimensions.get('window').height / 2,
            }}>
            <EmptyPage title="Chưa có địa chỉ nào" />
          </View>
        )}
      </ScrollView>
      {chooseAddress ? null : (
        <WScreenFooter>
          <AppButton
            containerStyle={{
              borderRadius: 8,
              marginHorizontal: 16,
              flex: 1,
              backgroundColor: ColorThemes.light.primary_main_color,
              justifyContent: 'center',
            }}
            borderColor="transparent"
            title={'Tạo địa chỉ mới'}
            onPress={() => {
              navigation.push(RootScreen.EditAddress);
            }}
          />
        </WScreenFooter>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  orderInfoRow: {
    flexDirection: 'row',
    padding: 16,
    borderBottomColor: '#EEEEEE',
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  orderInfoIcon: {
    width: 24,
    marginRight: 12,
  },
  orderInfoContent: {
    flex: 1,
    gap: 4,
  },
  orderInfoLabel: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  orderInfoValue: {
    ...TypoSkin.body3,
    color: '#757575',
  },
});
