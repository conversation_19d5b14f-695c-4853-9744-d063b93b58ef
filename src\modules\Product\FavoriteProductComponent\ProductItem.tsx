import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {FavoriteProduct} from '../../../redux/models/favoriteProduct';
import {faHeart, faCartShopping} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {ColorThemes} from '../../../assets/skin/colors';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import {unFavoriteProduct} from '../../../redux/actions/favoriteProductAction';
import {useDispatch} from 'react-redux';

const defaultImage = require('../../../assets/images/default_img.png');

interface ProductItemProps {
  item: FavoriteProduct;
}

const ProductItem = ({item}: ProductItemProps) => {
  if (!item.Product) return null;
  const product = item.Product;
  const dispatch = useDispatch();
  const onUnFavorite = (id: string) => {
    dispatch(unFavoriteProduct({id: id}) as any);
  };

  const addToCart = (id: string) => {
    // thêm vào giỏ hàng ở đây
  };

  return (
    <View style={styles.productItemContainer}>
      <View style={styles.imageContainer}>
        <Image
          source={product.Img ? {uri: product.Img} : defaultImage}
          resizeMode="cover"
          style={styles.productImage}
        />
      </View>

      <View style={styles.infoActionContainer}>
        <View style={styles.infoContainer}>
          <Text style={styles.productName}>{product.Name}</Text>
          <Text style={styles.productPrice}>
            {product.Price.toLocaleString()}đ
          </Text>
        </View>

        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.actionButton, styles.favoriteButton]}
            onPress={() => onUnFavorite(item.Id)}>
            <FontAwesomeIcon
              icon={faHeart}
              size={16}
              color={ColorThemes.light.error_main_color}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.cartButton]}
            onPress={() => addToCart(item.Id)}>
            <AppSvg SvgSrc={iconSvg.carShopping} size={18} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  productItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    marginVertical: 8,
  },
  imageContainer: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  productImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  infoActionContainer: {
    flex: 1,
    height: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
  },
  infoContainer: {
    height: '100%',
    width: '50%',
  },
  productName: {
    fontSize: 14,
    color: '#333',
    fontWeight: 'bold',
  },
  productPrice: {
    fontSize: 14,
    marginTop: 8,
    color: '#666',
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  favoriteButton: {
    backgroundColor: '#FFD700',
  },
  cartButton: {
    backgroundColor: '#E0E0E0',
  },
});

export default ProductItem;
function dispatch(arg0: any) {
  throw new Error('Function not implemented.');
}
