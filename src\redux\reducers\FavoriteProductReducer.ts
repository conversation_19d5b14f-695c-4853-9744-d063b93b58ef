import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {Category} from '../models/category';
import {fetchCategories} from '../actions/categoryAction';
import {
  fetchFavoriteProducts,
  loadmoreFavoriteProducts,
  unFavoriteProduct,
} from '../actions/favoriteProductAction';
import {FavoriteProduct} from '../models/favoriteProduct';

interface FavoriteProductStoreState {
  data: FavoriteProduct[];
  totalCount: number;
  loading: boolean;
  loadingMore: boolean;
  size: number;
}

export type {FavoriteProductStoreState};

const initState: FavoriteProductStoreState = {
  data: [],
  totalCount: 0,
  loading: false,
  loadingMore: false,
  size: 2,
};

export const favoriteProductSlice = createSlice({
  name: 'favoriteProduct',
  initialState: initState,
  reducers: {
    setData: <K extends keyof FavoriteProductStoreState>(
      state: FavoriteProductStoreState,
      action: PayloadAction<{
        stateName: K;
        data: FavoriteProductStoreState[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
    onFetching: (
      state: FavoriteProductStoreState,
      action: PayloadAction<boolean>,
    ) => {
      state.loading = action.payload;
    },
    onFetchDone: (state: FavoriteProductStoreState) => {
      state.loading = false;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchFavoriteProducts.pending, state => {
      state.loading = true;
    });
    builder.addCase(
      fetchFavoriteProducts.fulfilled,
      (state: FavoriteProductStoreState, action: any) => {
        state.loading = false;
        state.data = action.payload.data;
        state.totalCount = action.payload.totalCount;
      },
    );
    builder.addCase(fetchFavoriteProducts.rejected, state => {
      state.loading = false;
    });
    builder.addCase(loadmoreFavoriteProducts.pending, state => {
      state.loadingMore = true;
    });
    builder.addCase(loadmoreFavoriteProducts.fulfilled, (state, action) => {
      state.loadingMore = false;
      state.data = [...state.data, ...action.payload];
    });
    builder.addCase(unFavoriteProduct.fulfilled, (state, action) => {
      state.data = state.data.filter(item => item.Id !== action.payload);
      state.totalCount = state.totalCount - 1;
    });
  },
});

export const {setData, onFetching, onFetchDone} = favoriteProductSlice.actions;

export default favoriteProductSlice.reducer;
