/* eslint-disable react-native/no-inline-styles */
import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Image,
  Pressable,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  FlatList,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {TypoSkin} from '../../../assets/skin/typography';
import {
  AppSvg,
  Checkbox,
  FBottomSheet,
  hideBottomSheet,
  showBottomSheet,
} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import {ColorThemes} from '../../../assets/skin/colors';
import ModalPromotion from './ModalPromotion';
import WScreenFooter from '../../../Screen/Layout/footer';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import ScreenHeader from '../../../Screen/Layout/header';
import {TextInput} from 'react-native-paper';
import ListItem from '../../Product/list/ListProductCreate';
import {fetchCategories} from '../../../redux/actions/categoryAction';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import ItemPromorionCard from './List/ShopPromorionItem';
import {faAngleRight} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {Item} from 'react-native-paper/lib/typescript/components/Drawer/Drawer';

const ShopPromortionComponent = () => {
  const popupRef = useRef<any>(null);
  const [activeButton, setActiveButton] = useState<string>('Tất cả');
  const [isShow, setIsShow] = useState<boolean>(false);
  const dispatch: AppDispatch = useDispatch();
  const {data} = useSelector((state: RootState) => state.category);
  const handleShow = () => {
    console.log('check132231');
    setIsShow(true);
  };

  const closeModal = () => {
    setIsShow(false);
  };

  useEffect(() => {
    dispatch(fetchCategories());
  }, []);
  return (
    <View style={styles.content}>
      <FBottomSheet ref={popupRef} />
      <View style={styles.PromotionMenu}>
        <Text style={styles.label}>Danh sách</Text>
        <TouchableOpacity style={styles.deleteButton}>
          <AppSvg SvgSrc={iconSvg.delete} size={12} />
          <Text style={styles.buttonText}>Xóa hàng loạt</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.updateButton}
          onPress={() => handleShow()}>
          <AppSvg SvgSrc={iconSvg.updateAll} size={12} />
          <Text style={styles.buttonTextSuccess}>Cập nhật hàng loạt</Text>
        </TouchableOpacity>
      </View>
      <ScrollView horizontal={true} style={styles.PromorionNabar}>
        <TouchableOpacity
          style={
            activeButton === 'Tất cả' ? styles.activeButton : styles.button
          }
          onPress={() => setActiveButton('Tất cả')}>
          <Text style={styles.buttonTextMenu}>Tất cả</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={
            activeButton === 'Thời trang' ? styles.activeButton : styles.button
          }
          onPress={() => setActiveButton('Thời trang')}>
          <Text style={styles.buttonTextMenu}>Thời trang</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={
            activeButton === 'Mỹ phẩm' ? styles.activeButton : styles.button
          }
          onPress={() => setActiveButton('Mỹ phẩm')}>
          <Text style={styles.buttonTextMenu}>Mỹ phẩm</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={
            activeButton === 'Nội thất' ? styles.activeButton : styles.button
          }
          onPress={() => setActiveButton('Nội thất')}>
          <Text style={styles.buttonTextMenu}>Nội thất</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={
            activeButton === 'Công nghệ' ? styles.activeButton : styles.button
          }
          onPress={() => setActiveButton('Công nghệ')}>
          <Text style={styles.buttonTextMenu}>Công nghệ</Text>
        </TouchableOpacity>
      </ScrollView>
      <View style={styles.menuDetail}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerText}></Text>
          <Text style={styles.headerText}>Ảnh</Text>
          <Text style={styles.headerText}>
            Sản phẩm <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
          <Text style={styles.headerText}>
            KM <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
          <Text style={styles.headerText}>
            TT <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
        </View>

        {/* Row 1 */}
        <View style={styles.row}>
          <View style={{}}>
            <Checkbox value={true} onChange={() => {}} size={24} />
          </View>
          <View style={styles.imageContainer}>
            <Image
              source={{
                uri: 'data:image/jpeg;base64,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',
              }} // Thay bằng URL ảnh thực tế
              style={styles.image}
            />
          </View>
          <Text style={styles.text}>Đồ thể thao</Text>
          <Text style={styles.textTwo}>0%</Text>
          <View style={styles.actions}>
            <TouchableOpacity>
              <AppSvg SvgSrc={iconSvg.editPromotion} size={24} />
            </TouchableOpacity>
            <TouchableOpacity>
              <AppSvg SvgSrc={iconSvg.deletePromotion} size={24} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <WScreenFooter style={{width: '100%', paddingHorizontal: 20}}>
        <TouchableOpacity
          style={styles.buyButton}
          onPress={() => {
            showBottomSheet({
              ref: popupRef,
              enableDismiss: true,
              children: <BottomSheetPromotion ref={popupRef} />,
            });
          }}>
          <Text style={styles.actionButtonText}>Thêm sản phẩm khuyến mại</Text>
        </TouchableOpacity>
      </WScreenFooter>
      <ModalPromotion
        isShow={isShow}
        closeModal={closeModal}
        svgSrc={iconSvg.updateAll}
        title="Cập nhật khuyến mãi hàng loạt"
      />
    </View>
  );
};

const BottomSheetPromotion = (ref: any) => {
  const {data} = useSelector((state: RootState) => state.category);
  const [selectedDataOne, setSelectedDataOne] = useState<string>('');
  const [dataCategory, setDataCategory] = useState<any[]>([]);

  useEffect(() => {
    if (data && data?.length > 0) {
      console.log('check-data', data);
      setDataCategory(data);
    }
  }, [data]);

  return (
    <Pressable
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 100,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        position: 'relative',
      }}>
      <InforHeader showBack={false} showAction={true} title={'Chọn danh mục'} />
      <KeyboardAvoidingView
        style={{flex: 1, position: 'relative'}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust for iOS and Android
        keyboardVerticalOffset={Platform.OS === 'ios' ? 65 : 0} // Offset for iOS
      >
        <View
          style={{
            padding: 10,
            backgroundColor: '#fff',
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 10,
            }}>
            <TextInput
              style={{
                flex: 1,
                backgroundColor: '#f0f0f0',
                borderRadius: 10,
                padding: 8,
                marginRight: 10,
                height: 20,
                color: 'black',
              }}
              placeholder="Search"
            />
            <TouchableOpacity>
              <Text
                style={{
                  color: '#007AFF',
                  fontSize: 16,
                }}>
                Đã chọn
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={{height: 1000, marginBottom: 50}}>
          <View style={{}}>
            <Text
              style={{
                marginLeft: 16,
                ...TypoSkin.body3,
                color: ColorThemes.light.primary_main_color,
              }}>
              Chọn tất cả danh mục
            </Text>
            <View style={{height: 1000}}>gfgfdg</View>
          </View>
          <Pressable style={{flex: 1}}></Pressable>
        </View>
        <View
          style={{
            flex: 1,
            marginBottom: 10,
            position: 'absolute',
            bottom: 2,
            width: '100%',
          }}>
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              alignItems: 'center',
              justifyContent: 'space-around',
              position: 'absolute',
              bottom: 10,
            }}>
            <TouchableOpacity style={styles.cancelButton}>
              <Text
                style={styles.buttonText}
                onPress={() => hideBottomSheet(ref)}>
                Đóng
              </Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton}>
              <Text style={styles.buttonTextTwo}>Xác nhận</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
  },
  PromotionMenu: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 20,
    paddingBottom: 4,
  },
  label: {
    ...TypoSkin.title3,
    marginRight: 26,
    fontWeight: '400',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.error_border_color,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 10,
    marginRight: 8,
  },
  updateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.success_border_color,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 10,
  },
  buttonText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 12,
    marginLeft: 4,
  },
  buttonTextTwo: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 12,
    marginLeft: 4,
  },
  buttonTextSuccess: {
    color: ColorThemes.light.success_main_color,
    fontSize: 12,
    marginLeft: 4,
  },
  PromorionNabar: {
    backgroundColor: '#fff',
    marginLeft: 16,
    marginTop: 12,
    marginBottom: 12,
    maxHeight: 24,
  },
  activeButton: {
    backgroundColor: ColorThemes.light.primary_background,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginLeft: 16,
  },
  button: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginLeft: 16,
  },
  buttonTextMenu: {
    color: ColorThemes.light.primary_sub_color,
    fontSize: 12,
    fontWeight: '400',
  },
  menuDetail: {
    flex: 1,
    padding: 10,
    width: '100%',
    backgroundColor: '#fff',
    marginLeft: 12,
    marginRight: 12,
    marginBottom: 12,
    marginTop: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  headerText: {
    fontWeight: 'bold',
    ...TypoSkin.body2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  imageContainer: {
    marginRight: 30,
    marginLeft: 30,
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  text: {
    flex: 1,
    ...TypoSkin.body3,
    marginLeft: '1%',
  },
  textTwo: {
    flex: 1,
    ...TypoSkin.body3,
    marginLeft: '15%',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  editIcon: {
    fontSize: 16,
    color: '#000',
    marginRight: 10,
  },
  checkIcon: {
    fontSize: 16,
    color: '#00cc00',
    marginRight: 10,
  },
  deleteIcon: {
    fontSize: 16,
    color: '#ff0000',
  },
  checkbox: {
    marginRight: 10,
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
  },

  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0', // Màu xám nhạt cho nút "Đóng"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ccc',
    width: '45%',
    alignItems: 'center',
    color: 'white',
  },
  confirmButton: {
    backgroundColor: '#007AFF', // Màu xanh cho nút "Xác nhận"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    width: '45%',
    alignItems: 'center',
    color: 'white',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingLeft: 10,
    paddingRight: 20,
    paddingTop: 10,
    paddingBottom: 10,
    height: 50,
    backgroundColor: 'red',
  },
  itemText: {
    fontSize: 20,
    color: 'black',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default ShopPromortionComponent;
