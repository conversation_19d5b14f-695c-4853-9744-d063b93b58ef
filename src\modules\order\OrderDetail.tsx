/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View, Text, ActivityIndicator} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NumberStatusIcon, Title} from '../../Config/Contanst';
import SearchBar from '../../components/shop/Search';
import ListCard from '../../components/Order/list/ListCard';
import {DataController} from '../../base/baseController';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {TypoSkin} from '../../assets/skin/typography';
import HeaderBackground from '../../components/shop/HeaderShop';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';

const OrderDetail = () => {
  const route = useRoute<any>();
  const [typeCard, setTypeCard] = useState<string>('');
  const [numberCard, setNumberCard] = useState<number>(0);
  const [numberCardSearch, setNumberCardSearch] = useState<number>(0);
  const [dataSearch, setDataSearch] = useState<string>('');
  const [dataSearchResult, setDataSearchResult] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const orderController = new DataController('Order');
  const shopInfo = useSelectorShopState().data;

  useEffect(() => {
    if (dataSearch) {
      setIsLoading(true);
      setTimeout(() => {
        if (route?.params?.type == Title.New) {
          handleSearch(dataSearch, 1);
        } else if (route?.params?.type == Title.Processing) {
          handleSearch(dataSearch, 2);
        } else if (route?.params?.type == Title.Done) {
          handleSearch(dataSearch, 3);
        } else if (route?.params?.type == Title.Cancel) {
          handleSearch(dataSearch, 4);
        }
        setIsLoading(false);
      }, 1000);
    }
  }, [dataSearch]);

  const handleSearch = async (dataSearch: string, status: number) => {
    if (dataSearch) {
      orderController
        .aggregateList({
          searchRaw: `@Name: (${dataSearch}) @ShopId: {${shopInfo[0].Id}} @Status: [${status}]`,
        })
        .then(res => {
          if (res.code == 200) {
            console.log('check-res', res.data);
            setDataSearchResult(res.data);
            setNumberCardSearch(res.data.length);
          }
        });
    }
  };
  useEffect(() => {
    if (route?.params?.type) {
      setTypeCard(route?.params?.type);
    }
  }, [route?.params?.type]);
  useEffect(() => {
    if (route?.params?.numberOrder) {
      setNumberCard(route?.params?.numberOrder);
    }
  }, [route?.params?.numberOrder]);

  return (
    <View style={styles.container}>
      {/* Header */}
      <InforHeader title={typeCard} />
      <SearchBar setDataSearch={setDataSearch} />
      {typeCard != Title.Done && (
        <View style={styles.orderInfo}>
          <Text style={styles.title}>Danh sách đơn hàng</Text>
          <Text style={styles.numberOrder}>
            {isLoading
              ? 'Đang tải dữ liệu...'
              : dataSearch?.length > 0
              ? numberCardSearch + ' đơn hàng'
              : numberCard + ' đơn hàng'}
          </Text>
        </View>
      )}
      {isLoading ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        <ListCard
          type={typeCard}
          setTypeCard={setTypeCard}
          dataSearchResult={dataSearchResult}
          dataSearch={dataSearch}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  orderInfo: {
    display: 'flex',
    marginLeft: 13,
  },
  title: {
    ...TypoSkin.title3,
    fontWeight: '500',
    fontFamily: 'roboto',
  },
  numberOrder: {
    ...TypoSkin.title4,
    marginTop: 10,
    color: '#999',
    marginBottom: 8,
  },
});

export default OrderDetail;
